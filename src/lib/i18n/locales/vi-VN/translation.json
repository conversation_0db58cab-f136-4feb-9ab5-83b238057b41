{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' hoặc '-1' không hết hạn.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(vd: `sh webui.sh --api`)", "(latest)": "(mới nhất)", "{{ models }}": "{{ mô hình }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}'s Chats", "{{webUIName}} Backend Required": "{{webUIName}} <PERSON><PERSON><PERSON> c<PERSON><PERSON>end", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "<PERSON><PERSON> hình tác vụ được sử dụng khi thực hiện các tác vụ như tạo tiêu đề cho cuộc trò chuyện và truy vấn tìm kiếm trên web", "a user": "người sử dụng", "About": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON><PERSON><PERSON>", "Account Activation Pending": "<PERSON><PERSON><PERSON> k<PERSON>n đang chờ kích ho<PERSON>t", "Actions": "Tác vụ", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Người dùng đang hoạt động", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add a model ID": "", "Add a short description about what this model does": "<PERSON><PERSON><PERSON><PERSON> mô tả ngắn về những khả năng của model", "Add a tag": "Thêm thẻ (tag)", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add custom prompt": "Thêm prompt tùy chỉnh", "Add Files": "<PERSON><PERSON><PERSON><PERSON>", "Add Group": "", "Add Memory": "<PERSON><PERSON><PERSON><PERSON> bộ nhớ", "Add Model": "Thêm model", "Add Reaction": "", "Add Tag": "Thê<PERSON> thẻ", "Add Tags": "thêm thẻ", "Add text content": "", "Add User": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "<PERSON><PERSON><PERSON> thay đổi cài đặt này sẽ áp dụng cho tất cả người sử dụng.", "admin": "quản trị viên", "Admin": "<PERSON><PERSON><PERSON><PERSON> trị", "Admin Panel": "<PERSON><PERSON> trị", "Admin Settings": "<PERSON><PERSON><PERSON> đặt hệ thống", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Quản trị viên luôn có quyền truy cập vào tất cả các tool; người dùng cần các tools được chỉ định cho mỗi mô hình trong workspace.", "Advanced Parameters": "<PERSON><PERSON><PERSON> tham số <PERSON> cao", "Advanced Params": "<PERSON><PERSON><PERSON> tham số <PERSON> cao", "All Documents": "<PERSON><PERSON><PERSON> cả tài liệu", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "<PERSON> phép <PERSON> nội dung chat", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "<PERSON> phép giọng nói không bản xứ", "Allow Temporary Chat": "<PERSON> phép <PERSON> nh<PERSON>p", "Allow User Location": "<PERSON> phép sử dụng vị trí người dùng", "Allow Voice Interruption in Call": "<PERSON> phép gián đoạn giọng nói trong cuộc gọi", "Allowed Endpoints": "", "Already have an account?": "Bạn đã có tài k<PERSON>n?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "tr<PERSON> lý", "and": "và", "and {{COUNT}} more": "", "and create a new shared link.": "và tạo một link chia sẻ mới", "api": "", "API Base URL": "Đường dẫn tới API (API Base URL)", "API Key": "API Key", "API Key created.": "Khóa API đã tạo", "API Key Endpoint Restrictions": "", "API keys": "API Keys", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "Tháng 4", "Archive": "<PERSON><PERSON><PERSON> tr<PERSON>", "Archive All Chats": "<PERSON><PERSON><PERSON> tất cả c<PERSON>c <PERSON>", "Archived Chats": "<PERSON><PERSON><PERSON>", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Bạn có chắc chắn không?", "Arena Models": "", "Artifacts": "", "Ask a question": "", "Assistant": "", "Attach file": "<PERSON><PERSON><PERSON> k<PERSON> file", "Attribute for Username": "", "Audio": "<PERSON><PERSON>", "August": "Tháng 8", "Authenticate": "", "Auto-Copy Response to Clipboard": "Tự động <PERSON> ch<PERSON>p <PERSON> hồi vào clipboard", "Auto-playback response": "<PERSON><PERSON> động phát lại phản hồi (Auto-playback)", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "Đường dẫn kết nối tới AUTOMATIC1111 (Base URL)", "AUTOMATIC1111 Base URL is required.": "Base URL của AUTOMATIC1111 là bắt buộc.", "Available list": "", "available!": "có sẵn!", "Azure AI Speech": "", "Azure Region": "", "Back": "Quay lại", "Bad": "", "Bad Response": "<PERSON><PERSON><PERSON> lời KHÔNG tốt", "Banners": "<PERSON><PERSON><PERSON><PERSON>", "Base Model (From)": "<PERSON><PERSON> hình cơ sở (từ)", "Batch Size (num_batch)": "", "before": "tr<PERSON><PERSON><PERSON>", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Khóa API tìm kiếm dũng cảm", "By {{name}}": "", "Bypass SSL verification for Websites": "Bỏ qua xác thực SSL cho các trang web", "Call": "Gọi", "Call feature is not supported when using Web STT engine": "<PERSON><PERSON><PERSON> năng gọi điện không được hỗ trợ khi sử dụng công cụ Web STT", "Camera": "", "Cancel": "Hủy bỏ", "Capabilities": "<PERSON><PERSON><PERSON> l<PERSON>", "Capture": "", "Certificate Path": "", "Change Password": "<PERSON><PERSON><PERSON>", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "<PERSON><PERSON><PERSON>", "Chat Background Image": "<PERSON><PERSON><PERSON>n trò chuy<PERSON>n", "Chat Bubble UI": "<PERSON><PERSON><PERSON> chat", "Chat Controls": "<PERSON><PERSON><PERSON><PERSON>", "Chat direction": "<PERSON><PERSON><PERSON>ng chat", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "Cha<PERSON>", "Check Again": "<PERSON><PERSON><PERSON>", "Check for updates": "<PERSON><PERSON><PERSON> tra cập nh<PERSON>t", "Checking for updates...": "<PERSON><PERSON> kiểm tra cập nhật...", "Choose a model before saving...": "<PERSON><PERSON><PERSON> mô hình trư<PERSON>c khi lưu...", "Chunk Overlap": "<PERSON><PERSON><PERSON> lấn (overlap)", "Chunk Params": "<PERSON><PERSON> số kh<PERSON>i (chunk)", "Chunk Size": "<PERSON><PERSON><PERSON> (size)", "Ciphers": "", "Citation": "<PERSON><PERSON><PERSON><PERSON> dẫn", "Clear memory": "Xóa bộ nhớ", "click here": "", "Click here for filter guides.": "", "Click here for help.": "<PERSON><PERSON>m vào đây để được trợ giúp.", "Click here to": "Nhấn vào đây để", "Click here to download user import template file.": "<PERSON>ấm vào đây để tải xuống tệp template của người dùng.", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "<PERSON>ấm vào đây để chọn", "Click here to select a csv file.": "<PERSON>hấn vào đây để chọn tệp csv", "Click here to select a py file.": "<PERSON><PERSON>ấn vào đây để chọn tệp py", "Click here to upload a workflow.json file.": "Bấm vào đ<PERSON>y để upload file worklow.json", "click here.": "bấm vào đây.", "Click on the user role button to change a user's role.": "Bấm vào nút trong cột VAI TRÒ để thay đổi quyền của người sử dụng.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Quyền ghi vào clipboard bị từ chối. Vui lòng kiểm tra cài đặt trên trình duyệt của bạn để được cấp quyền truy cập cần thiết.", "Clone": "<PERSON><PERSON><PERSON> b<PERSON>n", "Close": "Đ<PERSON><PERSON>", "Code execution": "", "Code formatted successfully": "<PERSON><PERSON> đ<PERSON><PERSON><PERSON> định dạng thành công", "Collection": "<PERSON><PERSON><PERSON> hợp mọi tài li<PERSON>u", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "Base URL của ComfyUI là bắt buộc.", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "<PERSON><PERSON><PERSON>", "Completions": "", "Concurrent Requests": "<PERSON><PERSON><PERSON> truy vấn đồng thời", "Configure": "", "Configure Models": "", "Confirm": "<PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON>", "Confirm your action": "<PERSON><PERSON><PERSON>ận hành động của bạn", "Confirm your new password": "", "Connections": "<PERSON><PERSON><PERSON>", "console": "", "Contact Admin for WebUI Access": "<PERSON><PERSON><PERSON> hệ với <PERSON>ản trị viên để đư<PERSON><PERSON> cấp quyền truy cập", "Content": "<PERSON><PERSON>i dung", "Content Extraction": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>t n<PERSON>i dung", "Context Length": "<PERSON><PERSON> dài ngữ cảnh (Context Length)", "Continue Response": "<PERSON><PERSON><PERSON><PERSON> tục trả lời", "Continue with {{provider}}": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với {{provider}}", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "Đã sao chép", "Copied shared chat URL to clipboard!": "Đã sao chép link chia sẻ trò chuyện vào clipboard!", "Copied to clipboard": "", "Copy": "Sao chép", "Copy last code block": "<PERSON>o chép khối mã cuối cùng", "Copy last response": "<PERSON><PERSON> ch<PERSON><PERSON> ph<PERSON>n hồi cuối cùng", "Copy Link": "Sao chép link", "Copy to clipboard": "", "Copying to clipboard was successful!": "Sao chép vào clipboard thành công!", "Create": "", "Create a knowledge base": "", "Create a model": "Tạo model", "Create Account": "<PERSON><PERSON><PERSON>", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "Tạo key mới", "Create new secret key": "Tạo key bí mật mới", "Created at": "<PERSON><PERSON><PERSON><PERSON> tạo vào lúc", "Created At": "Tạo lúc", "Created by": "Tạo bởi", "CSV Import": "Nạp CSV", "Current Model": "<PERSON><PERSON> hình hiện tại", "Current Password": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "Custom": "<PERSON><PERSON><PERSON> chỉnh", "Dark": "<PERSON><PERSON><PERSON>", "Database": "C<PERSON> sở dữ liệu", "December": "Tháng 12", "Default": "Mặc định", "Default (Open AI)": "", "Default (SentenceTransformers)": "Mặc định (SentenceTransformers)", "Default Model": "<PERSON> mặc định", "Default model updated": "<PERSON><PERSON> hình mặc định đã đư<PERSON><PERSON> cập nhật", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "<PERSON><PERSON> xuất prompt mặc định", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "<PERSON>ai trò mặc định", "Delete": "Xóa", "Delete a model": "<PERSON><PERSON><PERSON> mô hình", "Delete All Chats": "<PERSON><PERSON><PERSON>", "Delete All Models": "", "Delete chat": "<PERSON><PERSON><PERSON> n<PERSON>i dung chat", "Delete Chat": "Xóa chat", "Delete chat?": "Xóa chat?", "Delete folder?": "", "Delete function?": "Xóa function?", "Delete Message": "", "Delete prompt?": "Xóa prompt?", "delete this link": "Xóa link này", "Delete tool?": "Xóa tool?", "Delete User": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "Deleted {{deleteModelTag}}": "Đ<PERSON> xóa {{deleteModelTag}}", "Deleted {{name}}": "<PERSON><PERSON> xóa {{name}}", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "<PERSON><PERSON>", "Disabled": "Đã tắt", "discover": "", "Discover a function": "Khám phá function", "Discover a model": "<PERSON><PERSON><PERSON><PERSON> phá model", "Discover a prompt": "<PERSON><PERSON><PERSON><PERSON> phá thêm prompt mới", "Discover a tool": "Khám phá tool", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON> k<PERSON>, tả<PERSON> về và khám phá thêm các function tùy chỉnh", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON>, tả<PERSON> về và khám phá thêm các prompt tùy chỉnh", "Discover, download, and explore custom tools": "<PERSON><PERSON><PERSON>, tả<PERSON> về và khám phá thêm các tool tùy chỉnh", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON>, t<PERSON><PERSON> về và khám phá thêm các model presets", "Dismissible": "<PERSON><PERSON> thể loại bỏ", "Display": "", "Display Emoji in Call": "Hi<PERSON>n thị Emoji trong cuộc g<PERSON>i", "Display the username instead of You in the Chat": "<PERSON><PERSON>n thị tên người sử dụng thay vì 'Bạn' trong nội dung chat", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Không cài đặt các functions từ các nguồn mà bạn không hoàn toàn tin tưởng.", "Do not install tools from sources you do not fully trust.": "Không cài đặt các tools từ những nguồn mà bạn không hoàn toàn tin tưởng.", "Document": "<PERSON><PERSON><PERSON> l<PERSON>", "Documentation": "<PERSON><PERSON><PERSON> l<PERSON>", "Documents": "<PERSON><PERSON><PERSON> l<PERSON>", "does not make any external connections, and your data stays securely on your locally hosted server.": "<PERSON><PERSON><PERSON><PERSON> thực hiện bất kỳ kết nối ngoài nào, và dữ liệu của bạn vẫn được lưu trữ an toàn trên máy chủ lưu trữ cục bộ của bạn.", "Don't have an account?": "<PERSON><PERSON><PERSON>ng có tài k<PERSON>n?", "don't install random functions from sources you don't trust.": "không cài đặt các function từ các nguồn mà bạn không tin tưởng.", "don't install random tools from sources you don't trust.": "không cài đặt các tools từ các nguồn mà bạn không tin tưởng.", "Done": "<PERSON><PERSON><PERSON> th<PERSON>", "Download": "<PERSON><PERSON><PERSON> về", "Download canceled": "Đã hủy download", "Download Database": "<PERSON><PERSON><PERSON> xuống <PERSON> sở dữ liệu", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "<PERSON><PERSON><PERSON> bất kỳ tệp nào ở đây để thêm vào nội dung chat", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "vd: '30s','10m'. Đơn vị thời gian hợp lệ là 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Chỉnh sửa", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "S<PERSON>a Memory", "Edit User": "Thay đổi thông tin người sử dụng", "Edit User Group": "", "ElevenLabs": "", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "", "Embedding Model": "<PERSON><PERSON> h<PERSON>nh embedding", "Embedding Model Engine": "<PERSON><PERSON><PERSON><PERSON> lý embedding", "Embedding model set to \"{{embedding_model}}\"": "<PERSON><PERSON> hình embedding đ<PERSON> đ<PERSON><PERSON><PERSON> thiết lập thành \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "<PERSON> phép Chia sẻ Cộng đồng", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "<PERSON> phép p<PERSON><PERSON><PERSON> hồ<PERSON>, đ<PERSON><PERSON> giá", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "<PERSON> phép đăng ký mới", "Enable Web Search": "<PERSON> phép tìm kiếm Web", "Enabled": "<PERSON><PERSON> bật", "Engine": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON> bảo tệp CSV của bạn bao gồm 4 cột theo thứ tự sau: Name, Email, Password, Role.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON><PERSON> yêu cầu của {{role}} ở đây", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON><PERSON> chi tiết về bản thân của bạn để LLMs có thể nhớ", "Enter api auth string (e.g. username:password)": "<PERSON><PERSON><PERSON><PERSON> chuỗi xác thực api (ví dụ: username: mật khẩu)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Nhập API key cho Brave Search", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "Nhập <PERSON> chồng lấn (overlap)", "Enter Chunk Size": "<PERSON><PERSON><PERSON><PERSON>", "Enter description": "", "Enter Github Raw URL": "Nhập URL cho Github Raw", "Enter Google PSE API Key": "Nhập Google PSE API Key", "Enter Google PSE Engine Id": "Nhập Google PSE Engine Id", "Enter Image Size (e.g. 512x512)": "<PERSON><PERSON><PERSON><PERSON> (vd: 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "<PERSON><PERSON><PERSON><PERSON> mã ngôn ngữ", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON><PERSON> thẻ mô hình (vd: {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON><PERSON><PERSON><PERSON> (vd: 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "Nhập Score", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Nhập Query URL cho Searxng", "Enter Seed": "", "Enter Serper API Key": "Nhập Serper API Key", "Enter Serply API Key": "Nhập Serply API Key", "Enter Serpstack API Key": "Nhập Serpstack API Key", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "<PERSON><PERSON>ập stop sequence", "Enter system prompt": "Nhập system prompt", "Enter Tavily API Key": "Nhập Tavily API Key", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Nhập URL cho  Tika Server", "Enter Top K": "Nhập Top K", "Enter URL (e.g. http://127.0.0.1:7860/)": "Nhập URL (vd: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Nhập URL (vd: http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> b<PERSON>n", "Enter Your Full Name": "<PERSON><PERSON><PERSON><PERSON> và Tên của bạn", "Enter your message": "<PERSON><PERSON><PERSON><PERSON> tin nhắn của bạn", "Enter your new password": "", "Enter Your Password": "<PERSON><PERSON><PERSON><PERSON> của bạn", "Enter your prompt": "", "Enter Your Role": "<PERSON><PERSON><PERSON><PERSON> vai trò của bạn", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Lỗi", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "<PERSON><PERSON><PERSON>", "Explore the cosmos": "", "Export": "<PERSON><PERSON><PERSON>", "Export All Archived Chats": "", "Export All Chats (All Users)": "<PERSON><PERSON><PERSON> về tất cả nội dung chat (tất cả mọi người)", "Export chat (.json)": "<PERSON><PERSON><PERSON> chat (.json)", "Export Chats": "<PERSON><PERSON><PERSON> nội dung chat về máy", "Export Config to JSON File": "", "Export Functions": "Tải Functions về máy", "Export Models": "Tải Models về máy", "Export Presets": "", "Export Prompts": "<PERSON><PERSON><PERSON> c<PERSON> prompt v<PERSON> máy", "Export to CSV": "", "Export Tools": "<PERSON><PERSON><PERSON> về máy", "External Models": "Các model ng<PERSON><PERSON><PERSON>", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "Lỗi khởi tạo API Key", "Failed to read clipboard contents": "<PERSON><PERSON>ông thể đọc nội dung clipboard", "Failed to save models configuration": "", "Failed to update settings": "Lỗi khi cập nhật các cài đặt", "February": "Tháng 2", "Feedback History": "", "Feedbacks": "", "File": "<PERSON><PERSON><PERSON>", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "<PERSON><PERSON> độ T<PERSON><PERSON> vă<PERSON> bản", "File not found.": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tệp.", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File uploaded successfully": "", "Files": "<PERSON><PERSON><PERSON>", "Filter is now globally disabled": "<PERSON><PERSON> lọc hiện đã bị vô hiệu hóa trên toàn hệ thống", "Filter is now globally enabled": "<PERSON><PERSON> lọc hiện đ<PERSON><PERSON><PERSON> kích hoạt trên toàn hệ thống", "Filters": "<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "<PERSON><PERSON><PERSON> hiện giả mạo vân tay: <PERSON><PERSON>ông thể sử dụng tên viết tắt làm hình đại diện. Mặc định là hình ảnh hồ sơ mặc định.", "Fluidly stream large external response chunks": "T<PERSON>yền tải các khối phản hồi bên ngoài lớn một cách trôi chảy", "Focus chat input": "Tập trung vào nội dung chat", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Frequency Penalty": "<PERSON><PERSON><PERSON> ph<PERSON>t tần số", "Function": "", "Function created successfully": "Function đư<PERSON><PERSON> tạo thành công", "Function deleted successfully": "Function đã bị xóa", "Function Description": "", "Function ID": "", "Function is now globally disabled": "Function hiện đã bị vô hiệu hóa trên toàn hệ thống", "Function is now globally enabled": "Function đã đ<PERSON><PERSON><PERSON> kích hoạt trên toàn hệ thống", "Function Name": "", "Function updated successfully": "Function đư<PERSON><PERSON> cập nhật thành công", "Functions": "", "Functions allow arbitrary code execution": "Các Function cho phép thực thi mã tùy ý", "Functions allow arbitrary code execution.": "Các Function cho phép thực thi mã tùy ý.", "Functions imported successfully": "Các function đã được nạp thành công", "General": "<PERSON>ài đặt chung", "General Settings": "<PERSON><PERSON><PERSON> h<PERSON>nh chung", "Generate Image": "<PERSON><PERSON>", "Generating search query": "<PERSON><PERSON><PERSON> truy vấn tìm kiếm", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "<PERSON><PERSON><PERSON> h<PERSON> thống", "Good Response": "<PERSON><PERSON><PERSON> lời tốt", "Google Drive": "", "Google PSE API Key": "Khóa API Google PSE", "Google PSE Engine Id": "ID công cụ Google PSE", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "h:mm a", "Haptic Feedback": "<PERSON><PERSON><PERSON> h<PERSON> x<PERSON> g<PERSON>c", "Harmful or offensive": "", "has no conversations.": "kh<PERSON><PERSON> có hội thoại", "Hello, {{name}}": "<PERSON>n chào {{name}}", "Help": "<PERSON><PERSON><PERSON> g<PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "Ẩn", "Host": "", "How can I help you today?": "Tôi có thể giúp gì cho bạn hôm nay?", "How would you rate this response?": "", "Hybrid Search": "<PERSON><PERSON><PERSON> k<PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Tôi thừa nhận rằng tôi đã đọc và tôi hiểu ý nghĩa của hành động của mình. Tôi nhận thức được những rủi ro liên quan đến việc thực thi mã tùy ý và tôi đã xác minh độ tin cậy của nguồn.", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON> (thử nghiệm)", "Image Generation Engine": "<PERSON><PERSON>ng cụ tạo <PERSON>", "Image Max Compression Size": "", "Image Settings": "Cài đặt ảnh", "Images": "<PERSON><PERSON><PERSON>", "Import Chats": "<PERSON><PERSON><PERSON> lại nội dung chat", "Import Config from JSON File": "", "Import Functions": "Nạp Functions", "Import Models": "Nạp model", "Import Presets": "", "Import Prompts": "<PERSON><PERSON><PERSON> c<PERSON> prompt l<PERSON><PERSON> <PERSON><PERSON>ống", "Import Tools": "<PERSON><PERSON><PERSON>", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON> gồm flag `--api` khi chạy stable-diffusion-webui", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Thông tin", "Input commands": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> c<PERSON> l<PERSON>", "Install from Github URL": "<PERSON>ài đặt từ Github URL", "Instant Auto-Send After Voice Transcription": "Tự động gửi ngay lập tức sau khi phiên dịch giọng nói", "Interface": "<PERSON><PERSON><PERSON>", "Invalid file format.": "", "Invalid Tag": "<PERSON> không hợp lệ", "is typing...": "", "January": "Tháng 1", "Jina API Key": "", "join our Discord for help.": "tham gia Discord của chúng tôi để được trợ giúp.", "JSON": "JSON", "JSON Preview": "<PERSON><PERSON> JSON", "July": "Tháng 7", "June": "Tháng 6", "JWT Expiration": "JWT <PERSON> h<PERSON>n", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON> kết n<PERSON>i", "Key": "", "Keyboard shortcuts": "<PERSON><PERSON><PERSON>", "Knowledge": "<PERSON><PERSON><PERSON> th<PERSON>", "Knowledge Access": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Label": "", "Landing Page Mode": "", "Language": "<PERSON><PERSON><PERSON>", "Last Active": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> g<PERSON><PERSON> n<PERSON>t", "Last Modified": "<PERSON><PERSON><PERSON> s<PERSON>a gần nh<PERSON>t", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Light": "<PERSON><PERSON><PERSON>", "Listening...": "<PERSON><PERSON> nghe...", "Local": "", "Local Models": "", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "<PERSON><PERSON><PERSON><PERSON> tạo bởi Cộng đồng OpenWebUI", "Make sure to enclose them with": "<PERSON><PERSON><PERSON> ch<PERSON>c chắn bao quanh chúng bằng", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON> b<PERSON>o xuất tệp Workflow.json đúng format API của ComfyUI.", "Manage": "<PERSON><PERSON><PERSON><PERSON> lý", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Quản lý Pipelines", "March": "Tháng 3", "Max Tokens (num_predict)": "Tokens tối đa (num_predict)", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Tối đa 3 mô hình có thể được tải xuống cùng lúc. <PERSON><PERSON> lòng thử lại sau.", "May": "Tháng 5", "Memories accessible by LLMs will be shown here.": "Memory có thể truy cập bởi LLMs sẽ hiển thị ở đây.", "Memory": "Memory", "Memory added successfully": "<PERSON> đã đ<PERSON><PERSON><PERSON> thêm thành công", "Memory cleared successfully": "<PERSON> đã bị xóa", "Memory deleted successfully": "Memory đã bị loại bỏ", "Memory updated successfully": "<PERSON> đã cập nhật thành công", "Merge Responses": "<PERSON><PERSON><PERSON> n<PERSON> c<PERSON>c p<PERSON><PERSON>n hồi", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Tin nhắn bạn gửi sau khi tạo liên kết sẽ không được chia sẻ. Người dùng có URL sẽ có thể xem cuộc trò chuyện được chia sẻ.", "Min P": "", "Minimum Score": "Score tối thiểu", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "Mô hình '{{modelName}}' đã đư<PERSON><PERSON> tải xuống thành công.", "Model '{{modelTag}}' is already in queue for downloading.": "Mô hình '{{modelTag}}' đã có trong hàng đợi để tải xuống.", "Model {{modelId}} not found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy <PERSON>ô hình {{modelId}}", "Model {{modelName}} is not vision capable": "Model {{modelName}} không có khả năng nhìn", "Model {{name}} is now {{status}}": "Model {{name}} bây giờ là {{status}}", "Model accepts image inputs": "", "Model created successfully!": "<PERSON> đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Đường dẫn hệ thống tệp mô hình đư<PERSON><PERSON> phát hiện. Tên viết tắt mô hình là bắt buộc để cập nhật, không thể tiếp tục.", "Model Filtering": "", "Model ID": "ID mẫu", "Model IDs": "", "Model Name": "", "Model not selected": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON>nh", "Model Params": "<PERSON><PERSON>", "Model Permissions": "", "Model updated successfully": "<PERSON> đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "Modelfile Content": "<PERSON><PERSON><PERSON> dung <PERSON><PERSON><PERSON>nh", "Models": "<PERSON><PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON><PERSON><PERSON>", "Name your knowledge base": "", "New Chat": "Tạo chat mới", "New folder": "", "New Password": "<PERSON><PERSON><PERSON> mới", "new-channel": "", "No content found": "", "No content to speak": "<PERSON><PERSON><PERSON><PERSON> có nội dung để nói", "No distance available": "", "No feedbacks found": "", "No file selected": "<PERSON><PERSON><PERSON> có tệp nào đ<PERSON><PERSON><PERSON> ch<PERSON>n", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "No search query generated": "<PERSON><PERSON><PERSON><PERSON> có truy vấn tìm kiếm nào đư<PERSON><PERSON> tạo ra", "No source available": "<PERSON><PERSON><PERSON><PERSON> có nguồn", "No users were found.": "", "No valves to update": "<PERSON><PERSON><PERSON> c<PERSON> <PERSON> nào đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "None": "Không ai", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Lưu ý: <PERSON><PERSON><PERSON> bạn đặt điểm (Score) tối thiểu thì tìm kiếm sẽ chỉ trả về những tài liệu có điểm lớn hơn hoặc bằng điểm tối thiểu.", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "<PERSON><PERSON><PERSON><PERSON> báo trên má<PERSON> t<PERSON> (Notification)", "November": "Tháng 11", "num_gpu (Ollama)": "", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "", "October": "Tháng 10", "Off": "Tắt", "Okay, Let's Go!": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> đầu thôi!", "OLED Dark": "OLED Dark", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "API Ollama bị vô hiệu hóa", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "On": "<PERSON><PERSON><PERSON>", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Chỉ ký tự số và gạch nối được phép trong chuỗi lệnh.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Rất tiếc! URL dường như không hợp lệ. <PERSON>ui lòng kiểm tra lại và thử lại.", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Rất tiếc! Bạn đang sử dụng một phương thức không được hỗ trợ (chỉ dành cho frontend). <PERSON><PERSON> lòng cung cấp phương thức cho WebUI từ phía backend.", "Open in full screen": "", "Open new chat": "Mở nội dung chat mới", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Phiên bản Open WebUI (v{{OPEN_WEBUI_VERSION}}) hiện thấp hơn phiên bản bắt buộc (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Cấu hình API OpenAI", "OpenAI API Key is required.": "Bắt bu<PERSON>c nhập API OpenAI Key.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "<PERSON><PERSON>u cầu URL/Key API OpenAI.", "or": "hoặc", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "<PERSON><PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "Tập tin PDF (.pdf)", "PDF Extract Images (OCR)": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON> từ PDF (OCR)", "pending": "đang chờ phê du<PERSON>t", "Permission denied when accessing media devices": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>y cập các thiết bị đa phư<PERSON>ng tiện bị từ chối", "Permission denied when accessing microphone": "<PERSON><PERSON><PERSON><PERSON> truy cập micrô bị từ chối", "Permission denied when accessing microphone: {{error}}": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>y cập micr<PERSON> bị từ chối: {{error}}", "Permissions": "", "Personalization": "Cá nhân hóa", "Pin": "<PERSON><PERSON> l<PERSON>", "Pinned": "Đã ghim", "Pioneer insights": "", "Pipeline deleted successfully": "Pipeline đã bị xóa", "Pipeline downloaded successfully": "Pipeline đã đư<PERSON><PERSON> tải về thành công", "Pipelines": "", "Pipelines Not Detected": "<PERSON><PERSON>a tìm thấy Pi<PERSON>ines", "Pipelines Valves": "", "Plain text (.txt)": "<PERSON><PERSON><PERSON> bản thô (.txt)", "Playground": "<PERSON><PERSON><PERSON> (Playground)", "Please carefully review the following warnings:": "<PERSON>ui lòng xem xét cẩn thận các cảnh báo sau:", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "30 ngày trước", "Previous 7 days": "7 ng<PERSON>y trước", "Profile Image": "Ảnh đại diện", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (ví dụ: <PERSON><PERSON><PERSON> kể cho tôi một sự thật thú vị về Đế chế La Mã)", "Prompt Content": "<PERSON>ội dung prompt", "Prompt created successfully": "", "Prompt suggestions": "Gợi ý prompt", "Prompt updated successfully": "", "Prompts": "Prompt", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "<PERSON><PERSON><PERSON> \"{{searchValue}}\" từ Ollama.com", "Pull a model from Ollama.com": "Tải mô hình từ Ollama.com", "Query Generation Prompt": "", "Query Params": "<PERSON><PERSON> s<PERSON> Truy vấn", "RAG Template": "Mẫu prompt cho RAG", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "Đ<PERSON><PERSON> ra loa", "Record voice": "<PERSON><PERSON> <PERSON><PERSON>", "Redirecting you to OpenWebUI Community": "<PERSON><PERSON> chuyển hướng bạn đến Cộng đồng OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON> coi bản thân mình như \"Người dùng\" (ví dụ: \"Người dùng đang học T<PERSON>ếng Tâ<PERSON> Ban <PERSON>ha\")", "References from": "", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON><PERSON> sinh lại câu trả lời", "Release Notes": "<PERSON><PERSON> tả nh<PERSON>ng cập nhật mới", "Relevance": "", "Remove": "Xóa", "Remove Model": "Xóa model", "Rename": "<PERSON><PERSON><PERSON> tên", "Reorder Models": "", "Repeat Last N": "Repeat Last N", "Reply in Thread": "", "Request Mode": "Request Mode", "Reranking Model": "Reranking Model", "Reranking model disabled": "Reranking model disabled", "Reranking model set to \"{{reranking_model}}\"": "Reranking model đ<PERSON><PERSON><PERSON> đặt thành \"{{reranking_model}}\"", "Reset": "<PERSON><PERSON><PERSON> bộ", "Reset All Models": "", "Reset Upload Directory": "<PERSON><PERSON><PERSON> to<PERSON>n bộ thư mục Upload", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "<PERSON>hông thể kích hoạt thông báo vì trang web không cấp quyền. <PERSON>ui lòng truy cập cài đặt trình duyệt của bạn để cấp quyền cần thiết.", "Response splitting": "", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "<PERSON>ai trò", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "<PERSON><PERSON><PERSON><PERSON>", "Running": "<PERSON><PERSON>", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "Lưu & Tạo", "Save & Update": "Lưu & Cập nhật", "Save As Copy": "", "Save Tag": "<PERSON><PERSON><PERSON> Thẻ", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Không còn hỗ trợ lưu trữ lịch sử chat trực tiếp vào bộ nhớ trình duyệt của bạn. Vui lòng dành thời gian để tải xuống và xóa lịch sử chat của bạn bằng cách nhấp vào nút bên dưới. <PERSON><PERSON><PERSON> lo lắng, bạn có thể dễ dàng nhập lại lịch sử chat của mình vào backend thông qua", "Scroll to bottom when switching between branches": "Cuộn xuống dưới cùng khi chuyển đổi giữa các nh<PERSON>h", "Search": "<PERSON><PERSON><PERSON>", "Search a model": "Tìm model", "Search Base": "", "Search Chats": "<PERSON><PERSON><PERSON> k<PERSON> c<PERSON>", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "<PERSON><PERSON><PERSON> k<PERSON>m Functions", "Search Knowledge": "", "Search Models": "Tìm model", "Search options": "", "Search Prompts": "Tìm prompt", "Search Result Count": "Số kết quả tìm kiếm", "Search Tools": "<PERSON><PERSON><PERSON>", "Search users": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON> tìm \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "URL truy vấn Searxng", "See readme.md for instructions": "Xem readme.md để biết hướng dẫn", "See what's new": "<PERSON><PERSON> nh<PERSON> cập nhật mới", "Seed": "Seed", "Select a base model": "<PERSON><PERSON><PERSON> một base model", "Select a engine": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "Select a function": "Chọn function", "Select a group": "", "Select a model": "<PERSON><PERSON><PERSON> mô hình", "Select a pipeline": "<PERSON><PERSON><PERSON> một quy trình", "Select a pipeline url": "<PERSON><PERSON>n url quy trình", "Select a tool": "Chọn tool", "Select Engine": "Chọn Engine", "Select Knowledge": "", "Select model": "<PERSON><PERSON><PERSON> model", "Select only one model to call": "Chọn model <PERSON><PERSON>i", "Selected model(s) do not support image inputs": "Model đư<PERSON><PERSON> lựa chọn không hỗ trợ đầu vào là hình ảnh", "Semantic distance to query": "", "Send": "<PERSON><PERSON><PERSON>", "Send a message": "", "Send a Message": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "Send message": "<PERSON><PERSON><PERSON> yêu c<PERSON>u", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "Tháng 9", "Serper API Key": "Khóa API Serper", "Serply API Key": "", "Serpstack API Key": "Khóa API Serpstack", "Server connection verified": "<PERSON><PERSON><PERSON> n<PERSON>i máy chủ đã đ<PERSON><PERSON><PERSON> xác <PERSON>h", "Set as default": "Đặt làm mặc định", "Set CFG Scale": "", "Set Default Model": "Đặt <PERSON>ô hình Mặc định", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> lập mô hình embedding (ví dụ: {{model}})", "Set Image Size": "Đặt <PERSON><PERSON><PERSON> th<PERSON>nh", "Set reranking model (e.g. {{model}})": "<PERSON><PERSON><PERSON><PERSON> lập mô hình reranking (ví dụ: {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "Đặt Số Bước", "Set Task Model": "Đặt <PERSON><PERSON> h<PERSON>nh <PERSON> vụ", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "Đặt G<PERSON><PERSON>ng nói", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Cài đặt", "Settings saved successfully!": "Cài đặt đã được lưu thành công!", "Share": "<PERSON><PERSON> sẻ", "Share Chat": "<PERSON><PERSON> sẻ Chat", "Share to OpenWebUI Community": "<PERSON>a sẻ đến Cộng đồng OpenWebUI", "Show": "<PERSON><PERSON><PERSON> thị", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Hiển thị thông tin của Quản trị viên trên màn hình hiển thị Tài khoản đang chờ xử lý", "Show shortcuts": "<PERSON><PERSON><PERSON> thị phím tắt", "Show your support!": "Thể hiện sự ủng hộ của bạn!", "Sign in": "<PERSON><PERSON><PERSON>", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "<PERSON><PERSON><PERSON> xu<PERSON>", "Sign up": "<PERSON><PERSON><PERSON> ký", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "Lỗi nhận dạng giọng nói: {{error}}", "Speech-to-Text Engine": "<PERSON><PERSON>ng cụ <PERSON>hận dạng <PERSON> nói", "Stop": "", "Stop Sequence": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Stream Chat Response": "", "STT Model": "", "STT Settings": "Cài đặt Nhận dạng G<PERSON>ng nói", "Success": "<PERSON><PERSON><PERSON><PERSON> công", "Successfully updated.": "<PERSON><PERSON> cập nhật thành công.", "Suggested prompts to get you started": "", "Support": "Hỗ trợ", "Support this plugin:": "Hỗ trợ plugin này:", "Sync directory": "", "System": "<PERSON><PERSON> th<PERSON>", "System Instructions": "", "System Prompt": "Prompt <PERSON><PERSON> thống (System Prompt)", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "<PERSON><PERSON><PERSON> đ<PERSON> ng<PERSON>", "Tavily API Key": "", "Temperature": "<PERSON><PERSON><PERSON> sáng tạo", "Template": "Mẫu", "Temporary Chat": "<PERSON><PERSON>", "Text Splitter": "", "Text-to-Speech Engine": "<PERSON><PERSON>ng cụ <PERSON>n Văn bản thành <PERSON> nói", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "<PERSON><PERSON>m ơn bạn đã gửi phản hồi!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "<PERSON><PERSON><PERSON> nhà phát triển đằng sau plugin này là những tình nguyện viên nhiệt huyết của cộng đồng. Nếu bạn thấy plugin này hữu <PERSON>, vui lòng cân nhắc đóng góp cho sự phát triển của nó.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "<PERSON><PERSON><PERSON><PERSON> (score) ph<PERSON>i có giá trị từ 0,0 (0%) đến 1,0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "Chủ đề", "Thinking...": "<PERSON><PERSON> suy luận...", "This action cannot be undone. Do you wish to continue?": "Hành động này không thể được hoàn tác. Bạn có muốn tiếp tục không?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "<PERSON>i<PERSON>u này đảm bảo rằng các nội dung chat có giá trị của bạn được lưu an toàn vào cơ sở dữ liệu backend của bạn. Cảm ơn bạn!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "<PERSON><PERSON><PERSON> là tính năng thử nghiệm, có thể không hoạt động như mong đợi và có thể thay đổi bất kỳ lúc nào.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "<PERSON>t này sẽ bị xóa", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> ph<PERSON><PERSON> nhập URL cho Tika Server ", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Mẹo: <PERSON><PERSON><PERSON> nhật nhiều khe biến liên tiếp bằng cách nhấn phím tab trong đầu vào trò chuyện sau mỗi việc thay thế.", "Title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "Title (e.g. Tell me a fun fact)": "Ti<PERSON>u đề (ví dụ: <PERSON><PERSON><PERSON> kể cho tôi một sự thật thú vị về...)", "Title Auto-Generation": "Tự động Tạo Tiêu đề", "Title cannot be an empty string.": "Tiêu đề không được phép bỏ trống", "Title Generation Prompt": "Prompt tạo tiêu đề", "TLS": "", "To access the available model names for downloading,": "<PERSON><PERSON> truy cập các tên mô hình có sẵn để tải xuống,", "To access the GGUF models available for downloading,": "<PERSON><PERSON> truy cập các mô hình GGUF có sẵn để tải xuống,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "<PERSON><PERSON> truy cập vui lòng liên hệ với quản trị viên.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> chọn các tá<PERSON> vụ, bạn ph<PERSON><PERSON> thêm chúng vào workspace \"Functions\" trước.", "To select filters here, add them to the \"Functions\" workspace first.": "<PERSON><PERSON> chọn các filters, bạn ph<PERSON>i thêm chúng vào workspace \"Functions\" trước.", "To select toolkits here, add them to the \"Tools\" workspace first.": "<PERSON><PERSON> chọn c<PERSON> took<PERSON>, bạn ph<PERSON>i thêm chúng vào workspace \"Tools\" trước.", "Toast notifications for new updates": "", "Today": "<PERSON><PERSON><PERSON> nay", "Toggle settings": "Bật/tắt cài đặt", "Toggle sidebar": "Bật/tắt thanh bên", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "Tool đã đ<PERSON><PERSON><PERSON> tạo thành công", "Tool deleted successfully": "<PERSON><PERSON> đã bị x<PERSON>a", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "<PERSON>l đã đ<PERSON><PERSON><PERSON> nạp thành công", "Tool Name": "", "Tool updated successfully": "<PERSON>l đã đ<PERSON><PERSON><PERSON> cập nhật thành công", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Tools là một hệ thống gọi function với việc thực thi mã tùy ý", "Tools have a function calling system that allows arbitrary code execution": "<PERSON><PERSON><PERSON> có hệ thống gọi function cho phép thực thi mã tùy ý", "Tools have a function calling system that allows arbitrary code execution.": "<PERSON><PERSON><PERSON> có hệ thống gọi function cho phép thực thi mã tùy ý.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Gặp vấn đề khi truy cập <PERSON>?", "TTS Model": "", "TTS Settings": "Cài đặt Chuyển văn bản thành <PERSON> nói", "TTS Voice": "", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Nhập URL Hugging Face Resolve (<PERSON><PERSON>i <PERSON>u<PERSON>)", "Uh-oh! There was an issue with the response.": "", "UI": "<PERSON><PERSON><PERSON>", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Bỏ ghim", "Unravel secrets": "", "Untagged": "", "Update": "<PERSON><PERSON><PERSON>", "Update and Copy Link": "Cập nhật và sao chép link", "Update for the latest features and improvements.": "", "Update password": "<PERSON><PERSON><PERSON> nh<PERSON>t mật kh<PERSON>u", "Updated": "", "Updated at": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "Updated At": "", "Upload": "", "Upload a GGUF model": "<PERSON><PERSON><PERSON> lên mô hình GGUF", "Upload directory": "", "Upload files": "", "Upload Files": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> lên m<PERSON> chủ", "Upload Pipeline": "", "Upload Progress": "T<PERSON><PERSON><PERSON> trình tải tệp lên hệ thống", "URL": "", "URL Mode": "Chế độ URL", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Sử dụng Gravatar", "Use groups to group your users and assign permissions.": "", "Use Initials": "Sử dụng tên viết tắt", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "<PERSON>ư<PERSON>i sử dụng", "User": "", "User location successfully retrieved.": "<PERSON><PERSON> truy xuất thành công vị trí của người dùng.", "Username": "", "Users": "<PERSON>ư<PERSON>i sử dụng", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "Sử dụng", "Valid time units:": "Đơn vị thời gian h<PERSON> lệ:", "Valves": "", "Valves updated": "<PERSON><PERSON><PERSON><PERSON>", "Valves updated successfully": "<PERSON><PERSON> cập nh<PERSON>t <PERSON> thành công", "variable": "<PERSON><PERSON><PERSON>", "variable to have them replaced with clipboard content.": "biến để có chúng được thay thế bằng nội dung clipboard.", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "<PERSON><PERSON><PERSON><PERSON> nói", "Voice Input": "", "Warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Warning:": "Cảnh báo:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Cảnh báo: <PERSON><PERSON><PERSON> cập nhật hoặc thay đổi embedding model, bạn sẽ cần cập nhật lại tất cả tài liệu.", "Web": "Web", "Web API": "", "Web Loader Settings": "Cài đặt Web Loader", "Web Search": "<PERSON><PERSON><PERSON> k<PERSON>", "Web Search Engine": "<PERSON><PERSON><PERSON> n<PERSON> k<PERSON> Web", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "Cài đặt WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "Th<PERSON>ng tin mới về", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "<PERSON><PERSON> độ màn hình rộng", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Workspace", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> viết một prompt (vd: Bạn là ai?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Vi<PERSON><PERSON> một tóm tắt trong vòng 50 từ cho [chủ đề hoặc từ khóa].", "Write something...": "", "Write your model template content here": "", "Yesterday": "<PERSON><PERSON><PERSON> qua", "You": "Bạn", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Bạn có thể cá nhân hóa các tương tác của mình với LLM bằng cách thêm bộ nhớ thông qua nút 'Quản lý' bên dư<PERSON>, là<PERSON> cho chúng hữu ích hơn và phù hợp với bạn hơn.", "You cannot upload an empty file.": "", "You have no archived conversations.": "Bạn chưa lưu trữ một nội dung chat nào", "You have shared this chat": "Bạn vừa chia sẻ chat này", "You're a helpful assistant.": "Bạn là một trợ lý hữu ích.", "Your account status is currently pending activation.": "<PERSON><PERSON><PERSON> khoản của bạn hiện đang ở trạng thái chờ kích hoạt.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Toàn bộ đóng góp của bạn sẽ được chuyển trực tiếp đến nhà phát triển plugin; Open WebUI không lấy bất kỳ tỷ lệ phần trăm nào. <PERSON><PERSON>, nền tảng được chọn tài trợ có thể có phí riêng.", "Youtube": "Youtube", "Youtube Loader Settings": "Cài đặt Youtube Loader"}