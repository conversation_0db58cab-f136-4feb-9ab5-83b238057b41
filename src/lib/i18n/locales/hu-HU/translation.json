{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' vagy '-1' ha nincs lej<PERSON>rat.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(pl. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(pl. `sh webui.sh --api`)", "(latest)": "(<PERSON><PERSON><PERSON><PERSON>)", "{{ models }}": "{{ modelle<PERSON> }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "{{webUIName}} Backend Required": "{{webUIName}} Backend szükséges", "*Prompt node ID(s) are required for image generation": "*Prompt node ID(k) szükségesek a képgeneráláshoz", "A new version (v{{LATEST_VERSION}}) is now available.": "<PERSON><PERSON> (v{{LATEST_VERSION}}) érhető el.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "A feladat modell olyan fela<PERSON><PERSON><PERSON><PERSON>, mint a beszélgetések címeinek generálása és webes keresési lekérdezések", "a user": "e<PERSON>", "About": "Névjegy", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "<PERSON>ók", "Account Activation Pending": "Fiók aktiválása folyamatban", "Actions": "Műveletek", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "Aktív felhasználók", "Add": "Hozzáadás", "Add a model ID": "", "Add a short description about what this model does": "<PERSON>j hozzá egy r<PERSON><PERSON>, hogy mit csinál ez a modell", "Add a tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Arena Model": "Arena modell hozzáadása", "Add Connection": "", "Add Content": "Tartalom <PERSON>", "Add content here": "Tartalom hozzáadása ide", "Add custom prompt": "<PERSON><PERSON><PERSON><PERSON> prompt ho<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Files": "Fájlok hozzáadása", "Add Group": "", "Add Memory": "Memória hozzáadása", "Add Model": "<PERSON><PERSON>", "Add Reaction": "", "Add Tag": "<PERSON><PERSON><PERSON><PERSON>", "Add Tags": "Címkék hozzáadása", "Add text content": "Szöveges tartalom hozzáadása", "Add User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Ezen beállítások módosítása minden felhasználóra érvényes lesz.", "admin": "admin", "Admin": "Admin", "Admin Panel": "Admin Panel", "Admin Settings": "<PERSON><PERSON>", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Az adminok mindig hozzáférnek minden eszközhöz; a felhasználóknak modellenként kell eszközöket hozzárendelni a munkaterületen.", "Advanced Parameters": "<PERSON><PERSON><PERSON>", "Advanced Params": "<PERSON><PERSON><PERSON>", "All Documents": "Minden dokumentum", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "Beszélgetések törlésének engedélyezése", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "<PERSON><PERSON> hely<PERSON> engedélyez<PERSON>e", "Allow Temporary Chat": "Ideig<PERSON>s be<PERSON> engedélyezése", "Allow User Location": "Felhasználói helyzet engedélyezése", "Allow Voice Interruption in Call": "Hang megszakítás engedélyezése hívás közben", "Allowed Endpoints": "", "Already have an account?": "<PERSON><PERSON><PERSON>?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "egy <PERSON>", "and": "és", "and {{COUNT}} more": "és még {{COUNT}} db", "and create a new shared link.": "és hozz létre egy új megosztott linket.", "api": "", "API Base URL": "API alap URL", "API Key": "API kulcs", "API Key created.": "API kulcs létrehozva.", "API Key Endpoint Restrictions": "", "API keys": "API kulcsok", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Archiválás", "Archive All Chats": "Minden beszélgetés archiválása", "Archived Chats": "<PERSON><PERSON><PERSON><PERSON>", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "Biztos vagy benne?", "Arena Models": "Arena modellek", "Artifacts": "Műtermékek", "Ask a question": "Kérdezz valamit", "Assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Attach file": "<PERSON><PERSON><PERSON><PERSON>ol<PERSON>", "Attribute for Username": "", "Audio": "Hang", "August": "<PERSON><PERSON><PERSON><PERSON>", "Authenticate": "", "Auto-Copy Response to Clipboard": "Válasz automatikus másolása a vágólapra", "Auto-playback response": "Automatikus válasz le<PERSON>zás", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 Api hitelesí<PERSON><PERSON>", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 alap URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 alap URL szükséges.", "Available list": "Elérhető lista", "available!": "elérhető!", "Azure AI Speech": "Azure <PERSON> beszéd", "Azure Region": "Azure régió", "Back": "<PERSON><PERSON><PERSON>", "Bad": "", "Bad Response": "Rossz válasz", "Banners": "<PERSON><PERSON>", "Base Model (From)": "Alap modell (Forrás)", "Batch Size (num_batch)": "<PERSON><PERSON><PERSON><PERSON> mé<PERSON> (num_batch)", "before": "<PERSON><PERSON><PERSON>", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave Search API kulcs", "By {{name}}": "", "Bypass SSL verification for Websites": "SSL ellenőrzés kihagyása weboldalakhoz", "Call": "Hívás", "Call feature is not supported when using Web STT engine": "A hívás funkció nem támogatott Web STT motor használatakor", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Capabilities": "Képességek", "Capture": "", "Certificate Path": "", "Change Password": "Jelszó módosítása", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat Background Image": "Beszélge<PERSON>s h<PERSON>ttérkép", "Chat Bubble UI": "Beszélgetés buborék felület", "Chat Controls": "Beszélgetés vezérlők", "Chat direction": "Beszélgetés iránya", "Chat Overview": "Beszélge<PERSON>s <PERSON>", "Chat Permissions": "", "Chat Tags Auto-Generation": "Beszélgetés címkék automatikus generálása", "Chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Check Again": "<PERSON><PERSON><PERSON><PERSON>", "Check for updates": "Frissítések keresése", "Checking for updates...": "Frissítések keresése...", "Choose a model before saving...": "Válassz modellt mentés előtt...", "Chunk Overlap": "<PERSON><PERSON>", "Chunk Params": "<PERSON><PERSON>", "Chunk Size": "<PERSON><PERSON>", "Ciphers": "", "Citation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clear memory": "Memória törlése", "click here": "", "Click here for filter guides.": "", "Click here for help.": "Kattints ide segítségért.", "Click here to": "Kattints ide", "Click here to download user import template file.": "Kattints ide a felhasználó importálási sablon letöltéséhez.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> ide, hogy többet tudj meg a faster-whisperr<PERSON>l és lásd az elérhető modelleket.", "Click here to select": "Kattints ide a kiválasztáshoz", "Click here to select a csv file.": "Kattints ide egy CSV fájl kiválasztásához.", "Click here to select a py file.": "Kattints ide egy py fájl kiválasztásához.", "Click here to upload a workflow.json file.": "Kattints ide egy workflow.json fájl feltöltéséhez.", "click here.": "kattints ide.", "Click on the user role button to change a user's role.": "Kattints a felhasználói szerep gombra a felhasználó szerepének módosításához.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Vágólap írási engedély megtagadva. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON><PERSON>rizd a böngésző beállításait a szükséges hozzáférés megadásához.", "Clone": "Klónozás", "Close": "Bezárás", "Code execution": "Kód végrehajtás", "Code formatted successfully": "<PERSON><PERSON><PERSON>", "Collection": "Gyűjtemény", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI alap URL", "ComfyUI Base URL is required.": "ComfyUI alap URL szükséges.", "ComfyUI Workflow": "ComfyUI munkafolyamat", "ComfyUI Workflow Nodes": "ComfyUI munkafolyamat csomópontok", "Command": "<PERSON><PERSON><PERSON>", "Completions": "Kiegészítések", "Concurrent Requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Configure": "", "Configure Models": "", "Confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Confirm Password": "<PERSON><PERSON><PERSON>ó megerősítése", "Confirm your action": "Erősítsd meg a műveletet", "Confirm your new password": "", "Connections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "console": "", "Contact Admin for WebUI Access": "Lépj kapcsolatba az adminnal a WebUI hozzáférésért", "Content": "Tartalom", "Content Extraction": "<PERSON><PERSON><PERSON><PERSON>", "Context Length": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>z", "Continue Response": "Válasz folytatása", "Continue with {{provider}}": "Folytatás {{provider}} szolgáltatóval", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogyan legyen felosztva az üzenet szövege a TTS kérésekhez. A 'Központozás' monda<PERSON><PERSON> bontja, a 'Bekezdések' bekezdésekre bontja, a 'Nincs' pedig egyetlen szövegként kezeli az üzenetet.", "Controls": "Vezérlők", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "M<PERSON>ol<PERSON>", "Copied shared chat URL to clipboard!": "Megosztott beszélgetés URL másolva a vágólapra!", "Copied to clipboard": "Vágólapra másolva", "Copy": "Másolás", "Copy last code block": "Utolsó kódblokk másolása", "Copy last response": "Utolsó válasz másolása", "Copy Link": "<PERSON>", "Copy to clipboard": "Másolás a vágólapra", "Copying to clipboard was successful!": "Sikeres másolás a vágólapra!", "Create": "", "Create a knowledge base": "", "Create a model": "<PERSON><PERSON>", "Create Account": "Fiók létrehozása", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "Tudás l<PERSON>hoz<PERSON>", "Create new key": "Új kulcs létrehozása", "Create new secret key": "Új titkos kulcs létrehozása", "Created at": "Létrehozva", "Created At": "Létrehozva", "Created by": "Létrehozta", "CSV Import": "CSV importálás", "Current Model": "Jelenlegi modell", "Current Password": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Dark": "<PERSON><PERSON><PERSON><PERSON>", "Database": "Adatb<PERSON><PERSON><PERSON>", "December": "December", "Default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default (Open AI)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Open AI)", "Default (SentenceTransformers)": "Alap<PERSON><PERSON><PERSON><PERSON>ett (SentenceTransformers)", "Default Model": "Alapértelmezett modell", "Default model updated": "Alapértelmezett modell frissítve", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "Alap<PERSON><PERSON><PERSON><PERSON>ett prompt java<PERSON><PERSON>", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "Alapértelmezett felhasználói szerep", "Delete": "Törlés", "Delete a model": "<PERSON><PERSON> törl<PERSON>", "Delete All Chats": "Minden beszélgetés törlése", "Delete All Models": "", "Delete chat": "Beszélgetés törlése", "Delete Chat": "Beszélgetés törlése", "Delete chat?": "Törli a beszélgetést?", "Delete folder?": "T<PERSON><PERSON><PERSON> a mapp<PERSON>t?", "Delete function?": "Törl<PERSON> a funkciót?", "Delete Message": "", "Delete prompt?": "T<PERSON><PERSON><PERSON> a promptot?", "delete this link": "link törlése", "Delete tool?": "Törli az eszközt?", "Delete User": "Felhasz<PERSON><PERSON><PERSON> törl<PERSON>", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} törölve", "Deleted {{name}}": "{{name}} tö<PERSON><PERSON><PERSON>", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "Le<PERSON><PERSON><PERSON>", "Disabled": "<PERSON><PERSON><PERSON>", "discover": "", "Discover a function": "<PERSON><PERSON><PERSON> f<PERSON>", "Discover a model": "<PERSON><PERSON>", "Discover a prompt": "Prompt felfedezése", "Discover a tool": "Eszköz felfedezése", "Discover wonders": "", "Discover, download, and explore custom functions": "<PERSON><PERSON><PERSON> fel, tölts le és fedezz fel egyéni funkciókat", "Discover, download, and explore custom prompts": "<PERSON><PERSON><PERSON> fel, tölts le és fedezz fel egyéni promptokat", "Discover, download, and explore custom tools": "<PERSON><PERSON>z fel, tölts le és fedezz fel egyéni eszközöket", "Discover, download, and explore model presets": "<PERSON><PERSON><PERSON> fel, tölts le és fedezz fel modell beállításokat", "Dismissible": "Elutasítható", "Display": "", "Display Emoji in Call": "Emoji megjeleníté<PERSON> hívásban", "Display the username instead of You in the Chat": "Felhasználónév megjelenítése a 'Te' helyett a beszélgeté<PERSON>ben", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "Ne telepíts funkciókat olyan <PERSON>, amelyekben nem bízol telje<PERSON>.", "Do not install tools from sources you do not fully trust.": "Ne telepíts eszközöket olyan forrásokból, amelyekben nem bízol teljesen.", "Document": "Dokumentum", "Documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "Documents": "Dokumentumok", "does not make any external connections, and your data stays securely on your locally hosted server.": "nem létesí<PERSON> k<PERSON> ka<PERSON>olatokat, és az adataid biztonságban maradnak a helyileg hosztolt szervereden.", "Don't have an account?": "Nincs még fiókod?", "don't install random functions from sources you don't trust.": "ne telepíts véletlenszerű funkciókat olyan forrásokból, amelyekben nem bízol.", "don't install random tools from sources you don't trust.": "ne telepíts véletlenszerű eszközöket olyan forrásokból, amelyekben nem bízol.", "Done": "<PERSON><PERSON><PERSON>", "Download": "Letöltés", "Download canceled": "Letöltés megszakítva", "Download Database": "Adatbázis letölt<PERSON>e", "Drag and drop a file to upload or select a file to view": "", "Draw": "<PERSON><PERSON><PERSON><PERSON>", "Drop any files here to add to the conversation": "Húzz ide fájlokat a beszélgetéshez való hozzáadáshoz", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "pl. '30s','10m'. Érvényes időegységek: 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "Szerkesztés", "Edit Arena Model": "Arena modell szerkesztése", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "Memória szerkesztése", "Edit User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Edit User Group": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "", "Embedding Batch Size": "Beágyazási köteg méret", "Embedding Model": "Beágyazási modell", "Embedding Model Engine": "Beágyazási modell motor", "Embedding model set to \"{{embedding_model}}\"": "Beágyazási modell beállítva: \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Közösségi megosztás engedélyezése", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "Üzenet értékelés engedélyezése", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "Új regisztrációk engedélyezése", "Enable Web Search": "Webes keresés engedélyezése", "Enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Engine": "Motor", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Győződj meg róla, hogy a CSV fájl tartalmazza ezt a 4 oszlopot ebben a sorrendben: Név, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "Enter {{role}} message here": "<PERSON>rd ide a {{role}} üzenetet", "Enter a detail about yourself for your LLMs to recall": "Adj meg egy részletet magadról, amit az LLM-ek megjegyezhetnek", "Enter api auth string (e.g. username:password)": "Add meg az API hitelesítési karakterláncot (pl. felhasználónév:jelszó)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Add meg a Brave Search API kulcsot", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "Add meg a CFG skálát (pl. 7.0)", "Enter Chunk Overlap": "Add meg a darab <PERSON>", "Enter Chunk Size": "Add meg a darab méretet", "Enter description": "Add meg a leí<PERSON>", "Enter Github Raw URL": "Add meg a Github Raw URL-t", "Enter Google PSE API Key": "Add meg a Google PSE API kulcsot", "Enter Google PSE Engine Id": "Add meg a Google PSE motor azonosítót", "Enter Image Size (e.g. 512x512)": "Add meg a kép méretet (pl. 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "Add meg a nyelvi kódokat", "Enter Model ID": "Add meg a modell a<PERSON>", "Enter model tag (e.g. {{modelTag}})": "Add meg a modell cím<PERSON>t (pl. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "Add meg a lépések szá<PERSON>t (pl. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "Add meg a mintavételezőt (pl. Euler a)", "Enter Scheduler (e.g. Karras)": "Add meg az ü<PERSON> (pl. <PERSON><PERSON><PERSON>)", "Enter Score": "Add meg a pontszámot", "Enter SearchApi API Key": "Add meg a SearchApi API kulcsot", "Enter SearchApi Engine": "Add meg a SearchApi motort", "Enter Searxng Query URL": "Add meg a Searxng lekérdezési URL-t", "Enter Seed": "", "Enter Serper API Key": "Add meg a Serper API kulcsot", "Enter Serply API Key": "Add meg a Serply API kulcsot", "Enter Serpstack API Key": "Add meg a Serpstack API kulcsot", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "Add meg a leállítási szekvenciát", "Enter system prompt": "Add meg a rendszer promptot", "Enter Tavily API Key": "Add meg a Tavily API kulcsot", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Add meg a Tika szerver URL-t", "Enter Top K": "Add meg a Top K értéket", "Enter URL (e.g. http://127.0.0.1:7860/)": "Add meg az URL-t (pl. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Add meg az URL-t (pl. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Add meg az email címed", "Enter Your Full Name": "Add meg a teljes neved", "Enter your message": "Írd be az üzeneted", "Enter your new password": "", "Enter Your Password": "Add meg a j<PERSON>d", "Enter your prompt": "", "Enter Your Role": "Add meg a szereped", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "Hiba", "ERROR": "HIBA", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "Kizárás", "Experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Explore the cosmos": "", "Export": "Exportálás", "Export All Archived Chats": "", "Export All Chats (All Users)": "Minden beszélgetés exportálása (minden felhasználó)", "Export chat (.json)": "Beszélgetés exportálása (.json)", "Export Chats": "Beszélgetések exportálása", "Export Config to JSON File": "Konfiguráció exportálása JSON fájlba", "Export Functions": "Funkciók exportálása", "Export Models": "Modellek exportálása", "Export Presets": "", "Export Prompts": "Promptok exportálása", "Export to CSV": "", "Export Tools": "Eszközök exportálása", "External Models": "Külső modellek", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "<PERSON><PERSON> hozz<PERSON>adni a fájlt.", "Failed to create API Key.": "<PERSON>em si<PERSON>lt létrehozni az API kulcsot.", "Failed to read clipboard contents": "<PERSON><PERSON> o<PERSON> a vágólap tartalmát", "Failed to save models configuration": "", "Failed to update settings": "<PERSON><PERSON> f<PERSON>íteni a beállításokat", "February": "<PERSON><PERSON><PERSON><PERSON>", "Feedback History": "Visszajelzés előzmények", "Feedbacks": "", "File": "<PERSON><PERSON><PERSON><PERSON>", "File added successfully.": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>.", "File content updated successfully.": "Fájl tartalom sikeresen frissítve.", "File Mode": "<PERSON><PERSON><PERSON><PERSON> mód", "File not found.": "Fájl nem található.", "File removed successfully.": "<PERSON><PERSON><PERSON><PERSON>távolít<PERSON>.", "File size should not exceed {{maxSize}} MB.": "A fájl mérete nem haladhatja meg a {{maxSize}} MB-ot.", "File uploaded successfully": "", "Files": "Fájlok", "Filter is now globally disabled": "A szűrő globálisan letiltva", "Filter is now globally enabled": "A szűrő globálisan engedélyezve", "Filters": "Szűrők", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Ujjlenyomat hamisítás észlelve: Nem lehet a kezdőbetűket avatárként használni. Alapértelmezett profilkép has<PERSON>.", "Fluidly stream large external response chunks": "Nagy külső válasz darabok folyamatos streamelése", "Focus chat input": "Chat bevitel fókuszálása", "Folder deleted successfully": "Mappa si<PERSON>esen tö<PERSON>", "Folder name cannot be empty": "A mappa neve nem lehet üres", "Folder name cannot be empty.": "A mappa neve nem lehet üres.", "Folder name updated successfully": "Mappa neve sikeresen frissítve", "Forge new paths": "", "Form": "Űrlap", "Format your variables using brackets like this:": "Formázd a változóidat zárójelekkel így:", "Frequency Penalty": "Gyakor<PERSON><PERSON><PERSON> bü<PERSON>s", "Function": "Funkció", "Function created successfully": "<PERSON><PERSON><PERSON>", "Function deleted successfully": "<PERSON><PERSON><PERSON> si<PERSON> tö<PERSON>", "Function Description": "", "Function ID": "", "Function is now globally disabled": "A funkció globális<PERSON> let<PERSON>", "Function is now globally enabled": "A funkció globálisan engedélyezve", "Function Name": "", "Function updated successfully": "<PERSON><PERSON><PERSON> si<PERSON>n frissítve", "Functions": "Funkciók", "Functions allow arbitrary code execution": "A funkciók tetszőleges kód végrehajtását teszik lehetővé", "Functions allow arbitrary code execution.": "A funkciók tetszőleges kód végrehajtását teszik lehetővé.", "Functions imported successfully": "Funkciók sikeresen importálva", "General": "<PERSON><PERSON>lán<PERSON>", "General Settings": "Általános be<PERSON>", "Generate Image": "<PERSON><PERSON><PERSON>", "Generating search query": "Keresési lekérdezés generálása", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Good Response": "<PERSON><PERSON>", "Google Drive": "", "Google PSE API Key": "Google PSE API kulcs", "Google PSE Engine Id": "Google PSE motor azonosító", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "h:mm a", "Haptic Feedback": "Tapintási visszajelzés", "Harmful or offensive": "", "has no conversations.": "ni<PERSON><PERSON><PERSON>.", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "Seg<PERSON><PERSON><PERSON>g", "Help us create the best community leaderboard by sharing your feedback history!": "Segíts nekünk a legjobb közösségi ranglista létrehozásában a visszajelzési előzményeid megosztásával!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Host": "", "How can I help you today?": "<PERSON>gyan segíthetek ma?", "How would you rate this response?": "", "Hybrid Search": "<PERSON><PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Elismere<PERSON>, hogy elolvastam és megértem a cselekedetem következményeit. Tisztában vagyok a tetszőleges kód végrehajtásával jár<PERSON> k<PERSON>, és ellenőriztem a forrás megbízhatóságát.", "ID": "Azonosító", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (kísérleti)", "Image Generation Engine": "Képgenerálási motor", "Image Max Compression Size": "", "Image Settings": "<PERSON><PERSON><PERSON>", "Images": "Képek", "Import Chats": "Beszélgetések importálása", "Import Config from JSON File": "Konfiguráció importálása JSON fájlból", "Import Functions": "Funkciók importálása", "Import Models": "Modellek importálása", "Import Presets": "", "Import Prompts": "Promptok importálása", "Import Tools": "Eszközök importálása", "Include": "Tartalmaz", "Include `--api-auth` flag when running stable-diffusion-webui": "Add hozzá a `--api-auth` kapcsolót a stable-diffusion-webui futtatásakor", "Include `--api` flag when running stable-diffusion-webui": "Add hozzá a `--api` kapcsolót a stable-diffusion-webui futtatásakor", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "Információ", "Input commands": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Install from Github URL": "Telepítés Github URL-ről", "Instant Auto-Send After Voice Transcription": "Azonnali automatikus küldés hangfelismerés után", "Interface": "<PERSON><PERSON><PERSON><PERSON>", "Invalid file format.": "Érvénytelen f<PERSON>lf<PERSON>.", "Invalid Tag": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is typing...": "", "January": "<PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "Csatlakozz a Discord szerverünkhöz segítségért.", "JSON": "JSON", "JSON Preview": "JSON előnézet", "July": "<PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON>", "JWT Expiration": "JWT lejárat", "JWT Token": "JWT token", "Kagi Search API Key": "", "Keep Alive": "<PERSON><PERSON><PERSON><PERSON><PERSON> fen<PERSON>", "Key": "", "Keyboard shortcuts": "Billentyűpar<PERSON>ok", "Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Knowledge Access": "", "Knowledge created successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "Knowledge deleted successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> tö<PERSON>.", "Knowledge reset successfully.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON> v<PERSON>llítva.", "Knowledge updated successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>n frissítve", "Label": "", "Landing Page Mode": "<PERSON><PERSON><PERSON><PERSON><PERSON> mód", "Language": "Nyelv", "Last Active": "Utoljára a<PERSON>ív", "Last Modified": "U<PERSON><PERSON><PERSON><PERSON><PERSON>", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "<PERSON><PERSON><PERSON><PERSON>", "Leave empty for unlimited": "Hagyja üresen a korlátlan használathoz", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Hagyja üresen az összes modell hasz<PERSON>z, vagy v<PERSON> ki konkrét modelleket", "Leave empty to use the default prompt, or enter a custom prompt": "Hagyja üresen az alapértelmezett prompt <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vagy adjon meg egyéni promptot", "Light": "<PERSON>il<PERSON><PERSON>", "Listening...": "Hallgatás...", "Local": "", "Local Models": "<PERSON><PERSON><PERSON>", "Lost": "Elveszett", "LTR": "LTR", "Made by OpenWebUI Community": "Az OpenWebUI közösség által készítve", "Make sure to enclose them with": "Győződjön meg róla, hogy körülveszi <PERSON>", "Make sure to export a workflow.json file as API format from ComfyUI.": "Győződjön meg róla, hogy exportál egy workflow.json fájlt API formátumban a ComfyUI-ból.", "Manage": "Kezelés", "Manage Arena Models": "Arena modellek kezelése", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "Folyamatok k<PERSON>", "March": "<PERSON><PERSON><PERSON><PERSON>", "Max Tokens (num_predict)": "Maximum tokenek (num_predict)", "Max Upload Count": "Maximum feltöltések száma", "Max Upload Size": "Maximum feltöltési méret", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Maximum 3 modell tölthető le egyszerre. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prób<PERSON><PERSON><PERSON> k<PERSON>.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Az LLM-ek által elérhető emlékek itt jelennek meg.", "Memory": "Memória", "Memory added successfully": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "Memory cleared successfully": "Memória si<PERSON>n tö<PERSON>", "Memory deleted successfully": "Memória si<PERSON>n tö<PERSON>", "Memory updated successfully": "Memória si<PERSON>n frissítve", "Merge Responses": "Válaszok egyesítése", "Message rating should be enabled to use this feature": "Az üzenetértékelésnek engedélyezve kell lennie ehhez a funkcióhoz", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "A link létrehozása után küldött üzenetei nem lesznek megosztva. A URL-lel rendelkező felhasználók megtekinthetik a megosztott beszélgetést.", "Min P": "<PERSON>", "Minimum Score": "Minimum pontszám", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "YYYY. MMMM DD.", "MMMM DD, YYYY HH:mm": "YYYY. MMMM DD. HH:mm", "MMMM DD, YYYY hh:mm:ss A": "YYYY. MMMM DD. hh:mm:ss A", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "A '{{modelName}}' modell sikeresen letöltve.", "Model '{{modelTag}}' is already in queue for downloading.": "A '{{modelTag}}' modell már a letöltési sorban van.", "Model {{modelId}} not found": "A {{modelId}} modell nem ta<PERSON>", "Model {{modelName}} is not vision capable": "A {{modelName}} modell nem ké<PERSON> k<PERSON>pfeldolgozásra", "Model {{name}} is now {{status}}": "A {{name}} modell most {{status}} állapotban van", "Model accepts image inputs": "A modell elfogad képbemenetet", "Model created successfully!": "<PERSON>l si<PERSON>esen létrehozva!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modell fájlrendszer útvonal észlelve. A modell rövid neve szükséges a frissítéshez, nem folytatható.", "Model Filtering": "", "Model ID": "<PERSON><PERSON>", "Model IDs": "", "Model Name": "<PERSON>l neve", "Model not selected": "<PERSON><PERSON><PERSON> kiválasztva modell", "Model Params": "<PERSON><PERSON>", "Model Permissions": "", "Model updated successfully": "<PERSON><PERSON> f<PERSON>", "Modelfile Content": "<PERSON><PERSON><PERSON><PERSON><PERSON> tartalo<PERSON>", "Models": "<PERSON>le<PERSON>", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "t<PERSON>bb", "More": "<PERSON><PERSON><PERSON>", "Name": "Név", "Name your knowledge base": "", "New Chat": "<PERSON><PERSON>", "New folder": "", "New Password": "<PERSON><PERSON>", "new-channel": "", "No content found": "<PERSON><PERSON> tarta<PERSON>", "No content to speak": "<PERSON><PERSON><PERSON> felo<PERSON><PERSON><PERSON><PERSON> tarta<PERSON>", "No distance available": "<PERSON><PERSON><PERSON>r<PERSON> távolság", "No feedbacks found": "<PERSON><PERSON> v<PERSON>", "No file selected": "<PERSON><PERSON><PERSON> k<PERSON>lasztva fájl", "No files found.": "<PERSON><PERSON> fájl.", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "<PERSON>em <PERSON> HTML, CSS vagy JavaScript tartalom.", "No knowledge found": "<PERSON><PERSON> t<PERSON>", "No model IDs": "", "No models found": "<PERSON><PERSON>l", "No models selected": "", "No results found": "<PERSON><PERSON><PERSON>", "No search query generated": "<PERSON><PERSON> keresési le<PERSON>é<PERSON>z<PERSON>", "No source available": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "No users were found.": "", "No valves to update": "<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON><PERSON> szelep", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Megjegyzés: Ha minimum pontszámot <PERSON> be, a keresés csak olyan dokumentumokat ad vissza, am<PERSON><PERSON> pontszáma nagyobb vagy egy<PERSON>ő a minimum pontszámmal.", "Notes": "Jegyzetek", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Értesítések", "November": "November", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "<PERSON><PERSON><PERSON>", "October": "Október", "Off": "<PERSON>", "Okay, Let's Go!": "Rend<PERSON>, kezd<PERSON><PERSON><PERSON>!", "OLED Dark": "OLED sötét", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API letiltva", "Ollama API settings updated": "", "Ollama Version": "<PERSON><PERSON><PERSON> verzi<PERSON>", "On": "Be", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "Csak alfanumerikus karakterek és kötőjelek engedélyezettek a parancssorban.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Csak gyűjtemények szerkeszthetők, hozzon létre új tudásbázist dokumentumok szerkesztéséhez/hozzáadásához.", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hoppá! Úgy tűnik, az URL érvénytelen. K<PERSON><PERSON><PERSON><PERSON>k, ellenő<PERSON>ze és próbálja újra.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hoppá! Még vannak feltöltés alatt álló fájlok. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, várja meg a feltöltés befejezését.", "Oops! There was an error in the previous response.": "Hoppá! Hiba történt az előző válaszban.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hoppá! Nem t<PERSON>mo<PERSON>ott módszert használ (csak frontend). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, szolgálja ki a WebUI-t a backend-ről.", "Open in full screen": "Megnyitás teljes k<PERSON>n", "Open new chat": "<PERSON><PERSON>", "Open WebUI uses faster-whisper internally.": "Az Open WebUI belsőleg a faster-whispert használja.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Az Open WebUI verzió (v{{OPEN_WEBUI_VERSION}}) alacsonyabb, mint a szükséges verzió (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API konfiguráció", "OpenAI API Key is required.": "OpenAI API kulcs szükséges.", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/kulcs szükséges.", "or": "vagy", "Organize your users": "", "OUTPUT": "KIMENET", "Output format": "<PERSON><PERSON><PERSON>", "Overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page": "oldal", "Password": "Je<PERSON><PERSON><PERSON>", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF dokumentum (.pdf)", "PDF Extract Images (OCR)": "PDF képek kinyerése (OCR)", "pending": "függőben", "Permission denied when accessing media devices": "Hozzáférés megtagadva a médiaeszközökhöz", "Permission denied when accessing microphone": "Hozzáférés megtagadva a mikrofonhoz", "Permission denied when accessing microphone: {{error}}": "Hozzáfé<PERSON>s megtagadva a mikrofonhoz: {{error}}", "Permissions": "", "Personalization": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pin": "Rög<PERSON><PERSON><PERSON><PERSON>", "Pinned": "Rögzítve", "Pioneer insights": "", "Pipeline deleted successfully": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>n tö<PERSON>", "Pipeline downloaded successfully": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>n letöltve", "Pipelines": "Folyamatok", "Pipelines Not Detected": "Folyamatok nem észlelhetők", "Pipelines Valves": "<PERSON><PERSON><PERSON><PERSON>", "Plain text (.txt)": "Egyszerű szöveg (.txt)", "Playground": "J<PERSON>tszó<PERSON>r", "Please carefully review the following warnings:": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gondosan tekintse át a következő figyelmeztetéseket:", "Please enter a prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy promptot", "Please fill in all fields.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse ki az összes mezőt.", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "Előző 30 nap", "Previous 7 days": "Előző 7 nap", "Profile Image": "Profilkép", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (pl. <PERSON><PERSON><PERSON> egy érdekes tényt a Római Birodalomról)", "Prompt Content": "Prompt tartalom", "Prompt created successfully": "", "Prompt suggestions": "Prompt javaslatok", "Prompt updated successfully": "", "Prompts": "Promptok", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "\"{{searchValue}}\" letöltése az Ollama.com-ról", "Pull a model from Ollama.com": "Modell letöltése az Ollama.com-ról", "Query Generation Prompt": "", "Query Params": "Lekérdezési paramé<PERSON>ek", "RAG Template": "RAG sablon", "Rating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Re-rank models by topic similarity": "Modellek <PERSON>jrarangsorolása téma has<PERSON>lóság alapján", "Read Aloud": "Fe<PERSON>lvas<PERSON>", "Record voice": "<PERSON> rögzí<PERSON>", "Redirecting you to OpenWebUI Community": "Átirányítás az OpenWebUI közösséghez", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Hivatkozzon magára \"Felhaszná<PERSON>ó\"-k<PERSON>t (pl. \"A Felhasználó spanyolul tanul\")", "References from": "Hivatkozások innen", "Refresh Token Expiration": "", "Regenerate": "Újragenerálás", "Release Notes": "Kiadási jegyzetek", "Relevance": "Relevancia", "Remove": "Eltávolítás", "Remove Model": "<PERSON><PERSON> eltávolítás<PERSON>", "Rename": "Átnevezés", "Reorder Models": "", "Repeat Last N": "Utolsó N ismétlése", "Reply in Thread": "", "Request Mode": "<PERSON><PERSON><PERSON><PERSON> mód", "Reranking Model": "Újrarangsoroló modell", "Reranking model disabled": "Újrarangsoroló modell let<PERSON>", "Reranking model set to \"{{reranking_model}}\"": "Újrarangsoroló modell be<PERSON><PERSON><PERSON><PERSON><PERSON> erre: \"{{reranking_model}}\"", "Reset": "Visszaállítás", "Reset All Models": "", "Reset Upload Directory": "Feltöltési könyvtár visszaállítása", "Reset Vector Storage/Knowledge": "Vektor tá<PERSON><PERSON>/tud<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "A válasz értesítések nem aktiválhatók, mert a weboldal engedélyei meg lettek tagadva. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> el a böngésző beállításaihoz a szükséges hozzáférés megadásához.", "Response splitting": "V<PERSON>lasz felosztás", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Formázott szövegbevitel a chathez", "RK": "RK", "Role": "Szerep", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Futtatás", "Running": "Fu<PERSON>", "Save": "Men<PERSON>s", "Save & Create": "Mentés és létrehozás", "Save & Update": "Mentés és frissítés", "Save As Copy": "Men<PERSON>s <PERSON>", "Save Tag": "<PERSON><PERSON><PERSON><PERSON>", "Saved": "Mentve", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "A csevegési naplók közvetlen mentése a böngésző tárolójába már nem támogatott. K<PERSON><PERSON><PERSON><PERSON><PERSON>, sz<PERSON><PERSON> egy percet a csevegési naplók letöltésére és törlésére az alábbi gomb megnyom<PERSON>ával. Ne aggódjon, könnyen újra importálhatja a csevegési naplókat a backend-be", "Scroll to bottom when switching between branches": "Görgetés az aljára ágak közötti váltáskor", "Search": "Keresés", "Search a model": "<PERSON><PERSON>", "Search Base": "", "Search Chats": "Beszélgetések keresése", "Search Collection": "Gyűjtemény k<PERSON>sése", "Search Filters": "", "search for tags": "címkék keresése", "Search Functions": "Funkciók keresése", "Search Knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Search Models": "Modellek keresése", "Search options": "", "Search Prompts": "Promptok keresése", "Search Result Count": "Keresési találatok száma", "Search Tools": "Eszközök keresése", "Search users": "", "SearchApi API Key": "SearchApi API kulcs", "SearchApi Engine": "SearchApi motor", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "Keresés: \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>: \"{{searchQuery}}\"", "Searxng Query URL": "Searxng lekérdezési URL", "See readme.md for instructions": "Lásd a readme.md fájlt az útmutatásért", "See what's new": "Újdonságok megtekintése", "Seed": "Seed", "Select a base model": "V<PERSON><PERSON><PERSON> egy al<PERSON>", "Select a engine": "Válasszon egy motort", "Select a function": "Válasszon egy funkciót", "Select a group": "", "Select a model": "Válasszon egy modellt", "Select a pipeline": "Válasszon egy foly<PERSON>t", "Select a pipeline url": "Válasszon egy folyamat URL-t", "Select a tool": "Válasszon egy eszközt", "Select Engine": "Motor kiválasztása", "Select Knowledge": "Tud<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ztás<PERSON>", "Select model": "<PERSON><PERSON>lasztás<PERSON>", "Select only one model to call": "Csak egy modellt válasszon ki hívásra", "Selected model(s) do not support image inputs": "A kiválasztott modell(ek) nem támogatják a képbemenetet", "Semantic distance to query": "Szemantikai távolság a lekérdezéshez", "Send": "<PERSON><PERSON><PERSON><PERSON>", "Send a message": "", "Send a Message": "Üzenet küldése", "Send message": "Üzenet küldése", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "A kérésben elküldi a `stream_options: { include_usage: true }` opciót.\nA támogatott szolgáltatók token használati információt küldenek vissza a válaszban, ha be van állítva.", "September": "Szeptember", "Serper API Key": "Serper API kulcs", "Serply API Key": "Serply API kulcs", "Serpstack API Key": "Serpstack API kulcs", "Server connection verified": "Szerverkapcsolat ellenőrizve", "Set as default": "Beállítás alapértelmezettként", "Set CFG Scale": "CFG skála beállítása", "Set Default Model": "Alapértelmezett modell be<PERSON>", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "Beágyazási modell be<PERSON>ll<PERSON> (pl. {{model}})", "Set Image Size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set reranking model (e.g. {{model}})": "Újrarangsoroló modell be<PERSON> (pl. {{model}})", "Set Sampler": "Mintavételező beállítása", "Set Scheduler": "Ütemező beállítása", "Set Steps": "Lépések beállítása", "Set Task Model": "Feladat modell beállí<PERSON>a", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Set whisper model": "Whisper <PERSON><PERSON>", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "Beállítások", "Settings saved successfully!": "Beállítások sikeresen mentve!", "Share": "Megosztás", "Share Chat": "Beszélgetés megosztása", "Share to OpenWebUI Community": "Megosztás az OpenWebUI közösséggel", "Show": "Mutat", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "Admin részletek megjelenítése a függő fiók átfedésben", "Show shortcuts": "Gyorsbillentyűk megjelenítése", "Show your support!": "Mutassa meg tá<PERSON>át!", "Sign in": "Bejelentkezés", "Sign in to {{WEBUI_NAME}}": "Bejelentkezés ide: {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "Kijelentkezés", "Sign up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign up to {{WEBUI_NAME}}": "Regisztráció ide: {{WEBUI_NAME}}", "Signing in to {{WEBUI_NAME}}": "Bejelentkezés ide: {{WEBUI_NAME}}", "sk-1234": "", "Source": "<PERSON><PERSON><PERSON>", "Speech Playback Speed": "<PERSON><PERSON><PERSON><PERSON>", "Speech recognition error: {{error}}": "Beszédfelismerési hiba: {{error}}", "Speech-to-Text Engine": "Beszéd-szöveg motor", "Stop": "Le<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stop Sequence": "Leállítási szekvencia", "Stream Chat Response": "Chat v<PERSON>lasz streamel<PERSON>e", "STT Model": "STT modell", "STT Settings": "STT beállítások", "Success": "Siker", "Successfully updated.": "Sikeresen frissítve.", "Suggested prompts to get you started": "", "Support": "Támogatás", "Support this plugin:": "Támogassa ezt a bővítményt:", "Sync directory": "K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "System": "Rendszer", "System Instructions": "Rendszer utasítások", "System Prompt": "<PERSON><PERSON><PERSON> prompt", "Tags Generation": "", "Tags Generation Prompt": "<PERSON><PERSON><PERSON><PERSON> prompt", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "Koppintson a megszakításhoz", "Tavily API Key": "Tavily API kulcs", "Temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Template": "Sablon", "Temporary Chat": "I<PERSON>ig<PERSON>s chat", "Text Splitter": "Szöveg felosztó", "Text-to-Speech Engine": "Szöveg-beszéd motor", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Köszönjük a visszajelzést!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "A bővítmény fejlesztői lelkes önkéntesek a közösségből. Ha hasznosnak találja ezt a bővítményt, k<PERSON><PERSON><PERSON><PERSON><PERSON>, fontolja meg a fejlesztéséhez való hozzájárulást.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Az értékelési ranglista az Elo értékelési rendszeren alapul és valós időben frissül.", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "A ranglista jelenleg béta verz<PERSON> van, és az algoritmus finomítása során módosíthatjuk az értékelési számításokat.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "A maximális fájlméret MB-ban. Ha a fájlméret meghaladja ezt a limitet, a fájl nem lesz feltöltve.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "A chatben egyszerre használható fájlok maximális szá<PERSON>. Ha a fájlok száma meghaladja ezt a limitet, a fájlok nem lesznek feltöltve.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "A pontszámnak 0,0 (0%) és 1,0 (100%) közötti értéknek kell lennie.", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "<PERSON><PERSON><PERSON>", "Thinking...": "Gondolkodik...", "This action cannot be undone. Do you wish to continue?": "Ez a mű<PERSON>et nem von<PERSON>ó v<PERSON>za. Szeretné folytatni?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Ez biztosítja, hogy értékes beszélgetései biztonságosan mentésre kerüljenek a backend adatbázisban. Köszönjük!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "<PERSON>z egy kís<PERSON><PERSON><PERSON>, le<PERSON><PERSON>, hogy nem a várt módon működik és bármikor változhat.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Ez az opció törli az összes meglévő fájlt a gyűjteményben és lecseréli őket az újonnan feltöltött fájlokkal.", "This response was generated by \"{{model}}\"": "Ezt a választ a \"{{model}}\" generálta", "This will delete": "Ez törölni fogja", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON>z törölni fogja a <strong>{{NAME}}</strong>-t <PERSON>s <strong>minden tarta<PERSON></strong>.", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Ez visszaállítja a tudásbázist és szinkronizálja az összes fájlt. Szeretné folytatni?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika szerver URL szükséges.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tipp: Frissítsen több változó helyet egymás után a tab billentyű megnyomásával a chat bevitelben minden helyettesítés után.", "Title": "Cím", "Title (e.g. Tell me a fun fact)": "Cím (pl. Mon<PERSON>j egy érdekes tényt)", "Title Auto-Generation": "Cím automatikus generál<PERSON>", "Title cannot be an empty string.": "A cím nem lehet üres karakterlánc.", "Title Generation Prompt": "<PERSON><PERSON><PERSON> prompt", "TLS": "", "To access the available model names for downloading,": "A letölthető modellek nevének eléréséhez,", "To access the GGUF models available for downloading,": "A letölthető GGUF modellek eléréséhez,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "A WebUI eléréséhez ké<PERSON>, forduljon az adminisztrátorhoz. Az adminisztrátorok az Admin Panelen keresztül kezelhetik a felhasználói státuszokat.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "A tudásbázis csatolásához először adja hozzá őket a \"Knowledge\" munkaterülethez.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Adatai védelme érdekében a visszajelzésből csak az értékelések, modell <PERSON>, címkék és metaadatok kerülnek megosztásra - a chat előzményei privátak maradnak és nem kerülnek megosztásra.", "To select actions here, add them to the \"Functions\" workspace first.": "A műveletek kiválasztásához először adja hozzá őket a \"Functions\" munkaterülethez.", "To select filters here, add them to the \"Functions\" workspace first.": "A szűrők kiválasztásához először adja hozzá őket a \"Functions\" munkaterülethez.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Az eszközkészletek kiválasztásához először adja hozzá őket a \"Tools\" munkaterülethez.", "Toast notifications for new updates": "Felugró értesítések az új frissítésekről", "Today": "Ma", "Toggle settings": "Beállítások be/ki", "Toggle sidebar": "Oldalsáv be/ki", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "Megőrzendő tokenek kontextus frissítéskor (num_keep)", "Tool created successfully": "Eszköz <PERSON> létrehozva", "Tool deleted successfully": "Eszköz si<PERSON>esen törölve", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "Eszköz <PERSON>", "Tool Name": "", "Tool updated successfully": "Eszköz si<PERSON>esen frissítve", "Tools": "Eszközök", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "Az eszközök olyan függvényhívó rendszert alkotnak, amely tetszőleges kód végrehajtását teszi lehetővé", "Tools have a function calling system that allows arbitrary code execution": "Az eszközök olyan függvényhívó rendszerrel rendelkeznek, amely lehetővé teszi tetszőleges kód végrehajtását", "Tools have a function calling system that allows arbitrary code execution.": "Az eszközök olyan függvényhívó rendszerrel rendelkeznek, amely lehetővé teszi tetszőleges kód végrehajtását.", "Top K": "Top K", "Top P": "Top P", "Transformers": "", "Trouble accessing Ollama?": "Problé<PERSON><PERSON><PERSON> van az Ollama elérésével?", "TTS Model": "TTS modell", "TTS Settings": "TTS beállítások", "TTS Voice": "TTS hang", "Type": "<PERSON><PERSON><PERSON>", "Type Hugging Face Resolve (Download) URL": "Adja meg a Hugging Face Resolve (Letöltési) URL-t", "Uh-oh! There was an issue with the response.": "", "UI": "Felhasználói felület", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "Rögzí<PERSON>s felo<PERSON>", "Unravel secrets": "", "Untagged": "Címkézetlen", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update and Copy Link": "Frissítés és link másolása", "Update for the latest features and improvements.": "Frissítsen a legújabb funkciókért és fejlesztésekért.", "Update password": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "Updated": "Frissítve", "Updated at": "Frissítve ekkor", "Updated At": "Frissítve ekkor", "Upload": "Feltöltés", "Upload a GGUF model": "GGUF modell feltöltése", "Upload directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Upload files": "Fájlok feltöltése", "Upload Files": "Fájlok feltöltése", "Upload Pipeline": "Pipeline feltöltése", "Upload Progress": "Feltöltési folyamat", "URL": "", "URL Mode": "URL mód", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "Haszná<PERSON>ja a '#' karaktert a prompt bevitelénél a tudásbázis betöltéséhez és felhasználásához.", "Use Gravatar": "Gravat<PERSON>", "Use groups to group your users and assign permissions.": "", "Use Initials": "Monogram használata", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User location successfully retrieved.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>e si<PERSON>n le<PERSON>.", "Username": "", "Users": "Felhasználók", "Using the default arena model with all models. Click the plus button to add custom models.": "Az alapértelmezett aréna modell használata az összes modellel. Kattintson a plusz gombra egyéni modellek hozzáadásához.", "Utilize": "<PERSON><PERSON><PERSON><PERSON>", "Valid time units:": "Érvényes időegységek:", "Valves": "Szelepek", "Valves updated": "Szelepek frissítve", "Valves updated successfully": "Szelepek sikeresen frissítve", "variable": "változó", "variable to have them replaced with clipboard content.": "v<PERSON><PERSON><PERSON><PERSON>, hogy a vágólap tartalmával helyettesítse őket.", "Version": "<PERSON><PERSON><PERSON><PERSON>", "Version {{selectedVersion}} of {{totalVersions}}": "{{selectedVersion}}. verzió a {{totalVersions}}-ból", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "Hang", "Voice Input": "Hangbevitel", "Warning": "Figyelmeztetés", "Warning:": "Figyelmeztetés:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Figyelmeztetés: Ha frissíti vagy megváltoztatja a beágyazási modellt, minden dokumentumot újra kell importálnia.", "Web": "Web", "Web API": "Web API", "Web Loader Settings": "Web betöltő beállítások", "Web Search": "Webes kere<PERSON>", "Web Search Engine": "<PERSON>es keresőmotor", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI beállítások", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "Whisper (helyi)", "Widescreen Mode": "Szélesvásznú mód", "Won": "Nyert", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "Munkaterület", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "<PERSON><PERSON><PERSON> egy prompt java<PERSON><PERSON>t (pl. <PERSON> vagy te?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Írjon egy 50 szavas összefoglalót a [téma vagy k<PERSON>]-ról.", "Write something...": "<PERSON><PERSON><PERSON> valamit...", "Write your model template content here": "", "Yesterday": "Tegnap", "You": "<PERSON><PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Egyszerre maximum {{maxCount}} fáj<PERSON>l tud csevegni.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Az LLM-ek<PERSON> való interakcióit személyre szabhatja emlékek hozzáadásával a lenti 'Kezelés' gomb seg<PERSON><PERSON>, <PERSON>gy azok még hasznosabbak és személyre szabottabbak lesznek.", "You cannot upload an empty file.": "<PERSON>em tölt<PERSON>t fel üres fájlt.", "You have no archived conversations.": "Nincsenek archivált <PERSON>.", "You have shared this chat": "Megosztotta ezt a beszélgetést", "You're a helpful assistant.": "<PERSON>n egy segít<PERSON>k<PERSON>z asszisztens.", "Your account status is currently pending activation.": "Fiókja jelenleg aktiválásra vár.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "A teljes hozzájárulása közvetlenül a bővítmény fejlesztőjéhez kerül; az Open WebUI nem vesz le százalékot. Azonban a választott támogatási platformnak lehetnek saját díjai.", "Youtube": "YouTube", "Youtube Loader Settings": "YouTube betöltő beállítások"}