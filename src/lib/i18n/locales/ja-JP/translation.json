{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' または '-1' で無期限。", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(例: `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(例: `sh webui.sh --api`)", "(latest)": "(最新)", "{{ models }}": "{{ モデル }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}} のチャット", "{{webUIName}} Backend Required": "{{webUIName}} バックエンドが必要です", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "新しいバージョンが利用可能です。", "A task model is used when performing tasks such as generating titles for chats and web search queries": "タスクモデルは、チャットやウェブ検索クエリのタイトルの生成などのタスクを実行するときに使用されます", "a user": "ユーザー", "About": "概要", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "アカウント", "Account Activation Pending": "アカウント承認待ち", "Actions": "アクション", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "アクティブユーザー", "Add": "追加", "Add a model ID": "", "Add a short description about what this model does": "このモデルの機能に関する簡単な説明を追加します", "Add a tag": "タグを追加", "Add Arena Model": "", "Add Connection": "", "Add Content": "コンテンツを追加", "Add content here": "ここへコンテンツを追加", "Add custom prompt": "カスタムプロンプトを追加", "Add Files": "ファイルを追加", "Add Group": "", "Add Memory": "メモリを追加", "Add Model": "モデルを追加", "Add Reaction": "", "Add Tag": "タグを追加", "Add Tags": "タグを追加", "Add text content": "コンテンツを追加", "Add User": "ユーザーを追加", "Add User Group": "", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "これらの設定を調整すると、すべてのユーザーに変更が適用されます。", "admin": "管理者", "Admin": "管理者", "Admin Panel": "管理者パネル", "Admin Settings": "管理者設定", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "管理者は全てのツールにアクセス出来ます。ユーザーはワークスペースのモデル毎に割り当てて下さい。", "Advanced Parameters": "詳細パラメーター", "Advanced Params": "高度なパラメータ", "All Documents": "全てのドキュメント", "All models deleted successfully": "", "Allow Chat Delete": "", "Allow Chat Deletion": "チャットの削除を許可", "Allow Chat Edit": "", "Allow File Upload": "", "Allow non-local voices": "ローカル以外のボイスを許可", "Allow Temporary Chat": "一時的なチャットを許可", "Allow User Location": "ユーザーロケーションの許可", "Allow Voice Interruption in Call": "通話中に音声の割り込みを許可", "Allowed Endpoints": "", "Already have an account?": "すでにアカウントをお持ちですか？", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "", "an assistant": "アシスタント", "and": "および", "and {{COUNT}} more": "", "and create a new shared link.": "し、新しい共有リンクを作成します。", "api": "", "API Base URL": "API ベース URL", "API Key": "API キー", "API Key created.": "API キーが作成されました。", "API Key Endpoint Restrictions": "", "API keys": "API キー", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "4月", "Archive": "アーカイブ", "Archive All Chats": "すべてのチャットをアーカイブする", "Archived Chats": "チャット記録", "archived-chat-export": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "よろしいですか？", "Arena Models": "", "Artifacts": "", "Ask a question": "質問して下さい。", "Assistant": "", "Attach file": "ファイルを添付する", "Attribute for Username": "", "Audio": "オーディオ", "August": "8月", "Authenticate": "", "Auto-Copy Response to Clipboard": "クリップボードへの応答の自動コピー", "Auto-playback response": "応答の自動再生", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111のAuthを入力", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 ベース URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 ベース URL が必要です。", "Available list": "利用可能リスト", "available!": "利用可能！", "Azure AI Speech": "AzureAIスピーチ", "Azure Region": "Azureリージョン", "Back": "戻る", "Bad": "", "Bad Response": "応答が悪い", "Banners": "バナー", "Base Model (From)": "ベースモデル (From)", "Batch Size (num_batch)": "バッチサイズ (num_batch)", "before": "より前", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Brave Search API Key": "Brave Search APIキー", "By {{name}}": "", "Bypass SSL verification for Websites": "SSL 検証をバイパスする", "Call": "コール", "Call feature is not supported when using Web STT engine": "", "Camera": "カメラ", "Cancel": "キャンセル", "Capabilities": "資格", "Capture": "", "Certificate Path": "", "Change Password": "パスワードを変更", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "chat": "", "Chat": "チャット", "Chat Background Image": "チャットの背景画像", "Chat Bubble UI": "チャットバブルUI", "Chat Controls": "チャットコントロール", "Chat direction": "チャットの方向", "Chat Overview": "チャット概要", "Chat Permissions": "", "Chat Tags Auto-Generation": "チャットタグの自動生成", "Chats": "チャット", "Check Again": "再確認", "Check for updates": "アップデートを確認", "Checking for updates...": "アップデートを確認しています...", "Choose a model before saving...": "保存する前にモデルを選択してください...", "Chunk Overlap": "チャンクオーバーラップ", "Chunk Params": "チャンクパラメーター", "Chunk Size": "チャンクサイズ", "Ciphers": "", "Citation": "引用文", "Clear memory": "メモリをクリア", "click here": "", "Click here for filter guides.": "", "Click here for help.": "ヘルプについてはここをクリックしてください。", "Click here to": "ここをクリックして", "Click here to download user import template file.": "ユーザーテンプレートをインポートするにはここをクリックしてください。", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to select": "選択するにはここをクリックしてください", "Click here to select a csv file.": "CSVファイルを選択するにはここをクリックしてください。", "Click here to select a py file.": "Pythonスクリプトファイルを選択するにはここをクリックしてください。", "Click here to upload a workflow.json file.": "workflow.jsonファイルをアップロードするにはここをクリックしてください。", "click here.": "ここをクリックしてください。", "Click on the user role button to change a user's role.": "ユーザーの役割を変更するには、ユーザー役割ボタンをクリックしてください。", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "クリップボードへの書き込み許可がありません。ブラウザ設定を確認し許可してください。", "Clone": "クローン", "Close": "閉じる", "Code execution": "", "Code formatted successfully": "コードフォーマットに成功しました", "Collection": "コレクション", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUIベースURL", "ComfyUI Base URL is required.": "ComfyUIベースURLが必要です。", "ComfyUI Workflow": "ComfyUIワークフロー", "ComfyUI Workflow Nodes": "ComfyUIワークフローノード", "Command": "コマンド", "Completions": "", "Concurrent Requests": "同時リクエスト", "Configure": "", "Configure Models": "", "Confirm": "確認", "Confirm Password": "パスワードの確認", "Confirm your action": "あなたのアクションの確認", "Confirm your new password": "", "Connections": "接続", "console": "", "Contact Admin for WebUI Access": "WEBUIへの接続について管理者に問い合わせ下さい。", "Content": "コンテンツ", "Content Extraction": "コンテンツ抽出", "Context Length": "コンテキストの長さ", "Continue Response": "続きの応答", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Controls": "コントロール", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "", "Copied": "コピー", "Copied shared chat URL to clipboard!": "共有チャットURLをクリップボードにコピーしました!", "Copied to clipboard": "クリップボードにコピーしました。", "Copy": "コピー", "Copy last code block": "最後のコードブロックをコピー", "Copy last response": "最後の応答をコピー", "Copy Link": "リンクをコピー", "Copy to clipboard": "", "Copying to clipboard was successful!": "クリップボードへのコピーが成功しました！", "Create": "", "Create a knowledge base": "", "Create a model": "モデルを作成する", "Create Account": "アカウントを作成", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "ナレッジベース作成", "Create new key": "新しいキーを作成", "Create new secret key": "新しいシークレットキーを作成", "Created at": "作成日時", "Created At": "作成日時", "Created by": "", "CSV Import": "CSVインポート", "Current Model": "現在のモデル", "Current Password": "現在のパスワード", "Custom": "カスタム", "Dark": "ダーク", "Database": "データベース", "December": "12月", "Default": "デフォルト", "Default (Open AI)": "デフォルト(OpenAI)", "Default (SentenceTransformers)": "デフォルト (SentenceTransformers)", "Default Model": "デフォルトモデル", "Default model updated": "デフォルトモデルが更新されました", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "デフォルトのプロンプトの提案", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default User Role": "デフォルトのユーザー役割", "Delete": "削除", "Delete a model": "モデルを削除", "Delete All Chats": "すべてのチャットを削除", "Delete All Models": "", "Delete chat": "チャットを削除", "Delete Chat": "チャットを削除", "Delete chat?": "チャットを削除しますか？", "Delete folder?": "", "Delete function?": "Functionを削除しますか？", "Delete Message": "", "Delete prompt?": "プロンプトを削除しますか？", "delete this link": "このリンクを削除します", "Delete tool?": "ツールを削除しますか？", "Delete User": "ユーザーを削除", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} を削除しました", "Deleted {{name}}": "{{name}}を削除しました", "Deleted User": "", "Describe your knowledge base and objectives": "", "Description": "説明", "Disabled": "無効", "discover": "", "Discover a function": "Functionを探す", "Discover a model": "モデルを探す", "Discover a prompt": "プロンプトを探す", "Discover a tool": "ツールを探す", "Discover wonders": "", "Discover, download, and explore custom functions": "カスタムFunctionを探してダウンロードする", "Discover, download, and explore custom prompts": "カスタムプロンプトを探してダウンロードする", "Discover, download, and explore custom tools": "カスタムツールを探てしダウンロードする", "Discover, download, and explore model presets": "モデルプリセットを探してダウンロードする", "Dismissible": "", "Display": "", "Display Emoji in Call": "コールで絵文字を表示", "Display the username instead of You in the Chat": "チャットで「あなた」の代わりにユーザー名を表示", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "信頼できないソースからFunctionをインストールしないでください。", "Do not install tools from sources you do not fully trust.": "信頼出来ないソースからツールをインストールしないでください。", "Document": "ドキュメント", "Documentation": "ドキュメント", "Documents": "ドキュメント", "does not make any external connections, and your data stays securely on your locally hosted server.": "外部接続を行わず、データはローカルでホストされているサーバー上に安全に保持されます。", "Don't have an account?": "アカウントをお持ちではありませんか？", "don't install random functions from sources you don't trust.": "信頼出来ないソースからランダムFunctionをインストールしないでください。", "don't install random tools from sources you don't trust.": "信頼出来ないソースからランダムツールをインストールしないでください。", "Done": "完了", "Download": "ダウンロード", "Download canceled": "ダウンロードをキャンセルしました", "Download Database": "データベースをダウンロード", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to add to the conversation": "会話を追加するには、ここにファイルをドロップしてください", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "例: '30秒'、'10分'。有効な時間単位は '秒'、'分'、'時間' です。", "e.g. A filter to remove profanity from text": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. Tools for performing various operations": "", "Edit": "編集", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Memory": "メモリを編集", "Edit User": "ユーザーを編集", "Edit User Group": "", "ElevenLabs": "", "Email": "メールアドレス", "Embark on adventures": "", "Embedding Batch Size": "埋め込みモデルバッチサイズ", "Embedding Model": "埋め込みモデル", "Embedding Model Engine": "埋め込みモデルエンジン", "Embedding model set to \"{{embedding_model}}\"": "埋め込みモデルを\"{{embedding_model}}\"に設定しました", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "コミュニティ共有を有効にする", "Enable Google Drive": "Google Driveの有効化", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "メッセージ評価を有効にする", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "", "Enable New Sign Ups": "新規登録を有効にする", "Enable Web Search": "ウェブ検索を有効にする", "Enabled": "有効", "Engine": "エンジン", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "CSVファイルに4つの列が含まれていることを確認してください: Name, Email, Password, Role.", "Enter {{role}} message here": "{{role}} メッセージをここに入力してください", "Enter a detail about yourself for your LLMs to recall": "LLM が記憶するために、自分についての詳細を入力してください", "Enter api auth string (e.g. username:password)": "API AuthStringを入力(例: Username:Password)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter Brave Search API Key": "Brave Search APIキーの入力", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "CFGスケースを入力してください (例: 7.0)", "Enter Chunk Overlap": "チャンクオーバーラップを入力してください", "Enter Chunk Size": "チャンクサイズを入力してください", "Enter description": "", "Enter Github Raw URL": "<PERSON><PERSON><PERSON> Raw URLを入力", "Enter Google PSE API Key": "Google PSE APIキーの入力", "Enter Google PSE Engine Id": "Google PSE エンジン ID を入力します。", "Enter Image Size (e.g. 512x512)": "画像サイズを入力してください (例: 512x512)", "Enter Jina API Key": "", "Enter Kagi Search API Key": "", "Enter language codes": "言語コードを入力してください", "Enter Model ID": "モデルIDを入力してください。", "Enter model tag (e.g. {{modelTag}})": "モデルタグを入力してください (例: {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "ステップ数を入力してください (例: 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "サンプラーを入力してください(e.g. Euler a)。", "Enter Scheduler (e.g. Karras)": "スケジューラーを入力してください。(e.g. <PERSON><PERSON><PERSON>)", "Enter Score": "スコアを入力してください", "Enter SearchApi API Key": "SearchApi API Keyを入力してください。", "Enter SearchApi Engine": "SearchApi Engineを入力してください。", "Enter Searxng Query URL": "SearxngクエリURLを入力", "Enter Seed": "", "Enter Serper API Key": "Serper APIキーの入力", "Enter Serply API Key": "Serply API Keyを入力してください。", "Enter Serpstack API Key": "Serpstack APIキーの入力", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter stop sequence": "ストップシーケンスを入力してください", "Enter system prompt": "システムプロンプト入力", "Enter Tavily API Key": "Tavily API Keyを入力してください。", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "Tika Server URLを入力してください。", "Enter Top K": "トップ K を入力してください", "Enter URL (e.g. http://127.0.0.1:7860/)": "URL を入力してください (例: http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL を入力してください (例: http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "メールアドレスを入力してください", "Enter Your Full Name": "フルネームを入力してください", "Enter your message": "メッセージを入力してください", "Enter your new password": "", "Enter Your Password": "パスワードを入力してください", "Enter your prompt": "", "Enter Your Role": "ロールを入力してください", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "エラー", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exclude": "", "Experimental": "実験的", "Explore the cosmos": "", "Export": "エクスポート", "Export All Archived Chats": "", "Export All Chats (All Users)": "すべてのチャットをエクスポート (すべてのユーザー)", "Export chat (.json)": "チャットをエクスポート(.json)", "Export Chats": "チャットをエクスポート", "Export Config to JSON File": "設定をJSONファイルでエクスポート", "Export Functions": "Functionのエクスポート", "Export Models": "モデルのエクスポート", "Export Presets": "", "Export Prompts": "プロンプトをエクスポート", "Export to CSV": "", "Export Tools": "ツールのエクスポート", "External Models": "外部モデル", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "", "Failed to create API Key.": "APIキーの作成に失敗しました。", "Failed to read clipboard contents": "クリップボードの内容を読み取れませんでした", "Failed to save models configuration": "", "Failed to update settings": "設定アップデート失敗", "February": "2月", "Feedback History": "", "Feedbacks": "", "File": "ファイル", "File added successfully.": "ファイル追加が成功しました。", "File content updated successfully.": "ファイルコンテンツ追加が成功しました。", "File Mode": "ファイルモード", "File not found.": "ファイルが見つかりません。", "File removed successfully.": "ファイル削除が成功しました。", "File size should not exceed {{maxSize}} MB.": "ファイルサイズ最大値{{maxSize}} MB", "File uploaded successfully": "", "Files": "ファイル", "Filter is now globally disabled": "グローバルフィルタが無効です。", "Filter is now globally enabled": "グローバルフィルタが有効です。", "Filters": "フィルター", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "指紋のなりすましが検出されました: イニシャルをアバターとして使用できません。デフォルトのプロファイル画像にデフォルト設定されています。", "Fluidly stream large external response chunks": "大規模な外部応答チャンクをスムーズにストリーミングする", "Focus chat input": "チャット入力をフォーカス", "Folder deleted successfully": "", "Folder name cannot be empty": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Forge new paths": "", "Form": "フォーム", "Format your variables using brackets like this:": "", "Frequency Penalty": "頻度ペナルティ", "Function": "", "Function created successfully": "Functionの作成が成功しました。", "Function deleted successfully": "Functionの削除が成功しました。", "Function Description": "", "Function ID": "", "Function is now globally disabled": "Functionはグローバルで無効です。", "Function is now globally enabled": "Functionはグローバルで有効です。", "Function Name": "", "Function updated successfully": "Functionのアップデートが成功しました。", "Functions": "", "Functions allow arbitrary code execution": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "Functionsのインポートが成功しました", "General": "一般", "General Settings": "一般設定", "Generate Image": "", "Generating search query": "検索クエリの生成", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "グローバル", "Good Response": "良い応答", "Google Drive": "", "Google PSE API Key": "Google PSE APIキー", "Google PSE Engine Id": "Google PSE エンジン ID", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "h:mm a": "h:mm a", "Haptic Feedback": "触覚フィードバック", "Harmful or offensive": "", "has no conversations.": "対話はありません。", "Hello, {{name}}": "こんにちは、{{name}} さん", "Help": "ヘルプ", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "非表示", "Host": "", "How can I help you today?": "今日はどのようにお手伝いしましょうか？", "How would you rate this response?": "", "Hybrid Search": "ブリッジ検索", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "Ignite curiosity": "", "Image Compression": "", "Image Generation (Experimental)": "画像生成 (実験的)", "Image Generation Engine": "画像生成エンジン", "Image Max Compression Size": "", "Image Settings": "画像設定", "Images": "画像", "Import Chats": "チャットをインポート", "Import Config from JSON File": "設定をJSONファイルからインポート", "Import Functions": "Functionのインポート", "Import Models": "モデルのインポート", "Import Presets": "", "Import Prompts": "プロンプトをインポート", "Import Tools": "ツールのインポート", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webuiを実行する際に`--api`フラグを含める", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "", "Info": "情報", "Input commands": "入力コマンド", "Install from Github URL": "Github URLからインストール", "Instant Auto-Send After Voice Transcription": "", "Interface": "インターフェース", "Invalid file format.": "", "Invalid Tag": "無効なタグ", "is typing...": "", "January": "1月", "Jina API Key": "", "join our Discord for help.": "ヘルプについては、Discord に参加してください。", "JSON": "JSON", "JSON Preview": "JSON プレビュー", "July": "7月", "June": "6月", "JWT Expiration": "JWT 有効期限", "JWT Token": "JWT トークン", "Kagi Search API Key": "", "Keep Alive": "キープアライブ", "Key": "", "Keyboard shortcuts": "キーボードショートカット", "Knowledge": "ナレッジベース", "Knowledge Access": "", "Knowledge created successfully.": "ナレッジベースの作成に成功しました", "Knowledge deleted successfully.": "ナレッジベースの削除に成功しました", "Knowledge reset successfully.": "ナレッジベースのリセットに成功しました", "Knowledge updated successfully": "ナレッジベースのアップデートに成功しました", "Label": "", "Landing Page Mode": "ランディングページモード", "Language": "言語", "Last Active": "最終アクティブ", "Last Modified": "", "Last reply": "", "Latest users": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Leave empty for unlimited": "空欄なら無制限", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "カスタムプロンプトを入力。空欄ならデフォルトプロンプト", "Light": "ライト", "Listening...": "", "Local": "", "Local Models": "ローカルモデル", "Lost": "", "LTR": "LTR", "Made by OpenWebUI Community": "OpenWebUI コミュニティによって作成", "Make sure to enclose them with": "必ず次で囲んでください", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "管理", "Manage Arena Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "パイプラインの管理", "March": "3月", "Max Tokens (num_predict)": "最大トークン数 (num_predict)", "Max Upload Count": "最大アップロード数", "Max Upload Size": "最大アップロードサイズ", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "同時にダウンロードできるモデルは最大 3 つです。後でもう一度お試しください。", "May": "5月", "Memories accessible by LLMs will be shown here.": "LLM がアクセスできるメモリはここに表示されます。", "Memory": "メモリ", "Memory added successfully": "メモリに追加されました。", "Memory cleared successfully": "メモリをクリアしました。", "Memory deleted successfully": "メモリを削除しました。", "Memory updated successfully": "メモリアップデート成功", "Merge Responses": "", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "リンクを作成した後、送信したメッセージは共有されません。URL を持つユーザーは共有チャットを閲覧できます。", "Min P": "", "Minimum Score": "最低スコア", "Mirostat": "ミロスタット", "Mirostat Eta": "ミロスタット Eta", "Mirostat Tau": "ミロスタット Tau", "MMMM DD, YYYY": "MMMM DD, YYYY", "MMMM DD, YYYY HH:mm": "MMMM DD, YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "モデル '{{modelName}}' が正常にダウンロードされました。", "Model '{{modelTag}}' is already in queue for downloading.": "モデル '{{modelTag}}' はすでにダウンロード待ち行列に入っています。", "Model {{modelId}} not found": "モデル {{modelId}} が見つかりません", "Model {{modelName}} is not vision capable": "モデル {{modelName}} は視覚に対応していません", "Model {{name}} is now {{status}}": "モデル {{name}} は {{status}} になりました。", "Model accepts image inputs": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "モデルファイルシステムパスが検出されました。モデルの短縮名が必要です。更新できません。", "Model Filtering": "", "Model ID": "モデルID", "Model IDs": "", "Model Name": "", "Model not selected": "モデルが選択されていません", "Model Params": "モデルパラメータ", "Model Permissions": "", "Model updated successfully": "", "Modelfile Content": "モデルファイルの内容", "Models": "モデル", "Models Access": "", "Models configuration saved successfully": "", "Mojeek Search API Key": "", "more": "", "More": "もっと見る", "Name": "名前", "Name your knowledge base": "", "New Chat": "新しいチャット", "New folder": "", "New Password": "新しいパスワード", "new-channel": "", "No content found": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No files found.": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No knowledge found": "ナレッジベースが見つかりません", "No model IDs": "", "No models found": "", "No models selected": "", "No results found": "結果が見つかりません", "No search query generated": "検索クエリは生成されません", "No source available": "使用可能なソースがありません", "No users were found.": "", "No valves to update": "", "None": "何一つ", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "注意：最小スコアを設定した場合、検索は最小スコア以上のスコアを持つドキュメントのみを返します。", "Notes": "", "Notification Sound": "通知音", "Notification Webhook": "", "Notifications": "デスクトップ通知", "November": "11月", "num_gpu (Ollama)": "", "num_thread (Ollama)": "", "OAuth ID": "", "October": "10月", "Off": "オフ", "Okay, Let's Go!": "OK、始めましょう！", "OLED Dark": "OLED ダーク", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API disabled": "Ollama API が無効になっています", "Ollama API settings updated": "", "Ollama Version": "Ollama バージョン", "On": "オン", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "コマンド文字列には英数字とハイフンのみが許可されています。", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "おっと！ URL が無効なようです。もう一度確認してやり直してください。", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "おっと！ サポートされていない方法 (フロントエンドのみ) を使用しています。バックエンドから WebUI を提供してください。", "Open in full screen": "", "Open new chat": "新しいチャットを開く", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API 設定", "OpenAI API Key is required.": "OpenAI API キーが必要です。", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Key が必要です。", "or": "または", "Organize your users": "", "OUTPUT": "", "Output format": "", "Overview": "", "page": "", "Password": "パスワード", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF ドキュメント (.pdf)", "PDF Extract Images (OCR)": "PDF 画像抽出 (OCR)", "pending": "保留中", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "マイクへのアクセス時に権限が拒否されました: {{error}}", "Permissions": "", "Personalization": "個人化", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "パイプライン", "Pipelines Not Detected": "パイプラインは検出されませんでした", "Pipelines Valves": "パイプラインバルブ", "Plain text (.txt)": "プレーンテキスト (.txt)", "Playground": "プレイグラウンド", "Please carefully review the following warnings:": "", "Please enter a prompt": "", "Please fill in all fields.": "", "Please select a model first.": "", "Port": "", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Previous 30 days": "前の30日間", "Previous 7 days": "前の7日間", "Profile Image": "プロフィール画像", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "プロンプト（例：ローマ帝国についての楽しい事を教えてください）", "Prompt Content": "プロンプトの内容", "Prompt created successfully": "", "Prompt suggestions": "プロンプトの提案", "Prompt updated successfully": "", "Prompts": "プロンプト", "Prompts Access": "", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com から \"{{searchValue}}\" をプル", "Pull a model from Ollama.com": "Ollama.com からモデルをプル", "Query Generation Prompt": "", "Query Params": "クエリパラメーター", "RAG Template": "RAG テンプレート", "Rating": "", "Re-rank models by topic similarity": "", "Read Aloud": "読み上げ", "Record voice": "音声を録音", "Redirecting you to OpenWebUI Community": "OpenWebUI コミュニティにリダイレクトしています", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refresh Token Expiration": "", "Regenerate": "再生成", "Release Notes": "リリースノート", "Relevance": "", "Remove": "削除", "Remove Model": "モデルを削除", "Rename": "名前を変更", "Reorder Models": "", "Repeat Last N": "最後の N を繰り返す", "Reply in Thread": "", "Request Mode": "リクエストモード", "Reranking Model": "モデルの再ランキング", "Reranking model disabled": "再ランキングモデルが無効です", "Reranking model set to \"{{reranking_model}}\"": "再ランキングモデルを \"{{reranking_model}}\" に設定しました", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "アップロードディレクトリをリセット", "Reset Vector Storage/Knowledge": "ベクターストレージとナレッジべーうをリセット", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "応答の分割", "Result": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "役割", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "", "Running": "", "Save": "保存", "Save & Create": "保存して作成", "Save & Update": "保存して更新", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "チャットログをブラウザのストレージに直接保存する機能はサポートされなくなりました。下のボタンをクリックして、チャットログをダウンロードして削除してください。ご心配なく。チャットログは、次の方法でバックエンドに簡単に再インポートできます。", "Scroll to bottom when switching between branches": "ブランチの切り替え時にボタンをスクロールする", "Search": "検索", "Search a model": "モデルを検索", "Search Base": "", "Search Chats": "チャットの検索", "Search Collection": "Collectionの検索", "Search Filters": "", "search for tags": "", "Search Functions": "Functionの検索", "Search Knowledge": "ナレッジベースの検索", "Search Models": "モデル検索", "Search options": "", "Search Prompts": "プロンプトを検索", "Search Result Count": "検索結果数", "Search Tools": "ツールの検索", "Search users": "", "SearchApi API Key": "SearchApiのAPIKey", "SearchApi Engine": "SearchApiエンジン", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searxng Query URL": "Searxng クエリ URL", "See readme.md for instructions": "手順については readme.md を参照してください", "See what's new": "新機能を見る", "Seed": "シード", "Select a base model": "基本モデルの選択", "Select a engine": "エンジンの選択", "Select a function": "Functionの選択", "Select a group": "", "Select a model": "モデルを選択", "Select a pipeline": "パイプラインの選択", "Select a pipeline url": "パイプラインの URL を選択する", "Select a tool": "ツールの選択", "Select Engine": "エンジンの選択", "Select Knowledge": "ナレッジベースの選択", "Select model": "モデルを選択", "Select only one model to call": "", "Selected model(s) do not support image inputs": "一部のモデルは画像入力をサポートしていません", "Semantic distance to query": "", "Send": "送信", "Send a message": "", "Send a Message": "メッセージを送信", "Send message": "メッセージを送信", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "9月", "Serper API Key": "Serper APIキー", "Serply API Key": "", "Serpstack API Key": "Serpstack APIキー", "Server connection verified": "サーバー接続が確認されました", "Set as default": "デフォルトに設定", "Set CFG Scale": "", "Set Default Model": "デフォルトモデルを設定", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "埋め込みモデルを設定します（例：{{model}}）", "Set Image Size": "画像サイズを設定", "Set reranking model (e.g. {{model}})": "モデルを設定します（例：{{model}}）", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "ステップを設定", "Set Task Model": "タスクモデルの設定", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "音声を設定", "Set whisper model": "", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "", "Sets the size of the context window used to generate the next token. (Default: 2048)": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "設定", "Settings saved successfully!": "設定が正常に保存されました！", "Share": "共有", "Share Chat": "チャットを共有", "Share to OpenWebUI Community": "OpenWebUI コミュニティに共有", "Show": "表示", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show shortcuts": "表示", "Show your support!": "", "Sign in": "サインイン", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "サインアウト", "Sign up": "サインアップ", "Sign up to {{WEBUI_NAME}}": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Source": "ソース", "Speech Playback Speed": "音声の再生速度", "Speech recognition error: {{error}}": "音声認識エラー: {{error}}", "Speech-to-Text Engine": "音声テキスト変換エンジン", "Stop": "", "Stop Sequence": "ストップシーケンス", "Stream Chat Response": "", "STT Model": "STTモデル", "STT Settings": "STT設定", "Success": "成功", "Successfully updated.": "正常に更新されました。", "Suggested prompts to get you started": "", "Support": "", "Support this plugin:": "", "Sync directory": "", "System": "システム", "System Instructions": "", "System Prompt": "システムプロンプト", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "", "Tap to interrupt": "", "Tavily API Key": "", "Temperature": "温度", "Template": "テンプレート", "Temporary Chat": "一時的なチャット", "Text Splitter": "", "Text-to-Speech Engine": "テキスト音声変換エンジン", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "ご意見ありがとうございます！", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "スコアは0.0(0%)から1.0(100%)の間の値にしてください。", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "", "Theme": "テーマ", "Thinking...": "思考中...", "This action cannot be undone. Do you wish to continue?": "このアクションは取り消し不可です。続けますか？", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "これは、貴重な会話がバックエンドデータベースに安全に保存されることを保証します。ありがとうございます！", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "実験的機能であり正常動作しない場合があります。", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ヒント: 各置換後にチャット入力で Tab キーを押すことで、複数の変数スロットを連続して更新できます。", "Title": "タイトル", "Title (e.g. Tell me a fun fact)": "タイトル (例: 楽しい事を教えて)", "Title Auto-Generation": "タイトル自動生成", "Title cannot be an empty string.": "タイトルは空文字列にできません。", "Title Generation Prompt": "タイトル生成プロンプト", "TLS": "", "To access the available model names for downloading,": "ダウンロード可能なモデル名にアクセスするには、", "To access the GGUF models available for downloading,": "ダウンロード可能な GGUF モデルにアクセスするには、", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "今日", "Toggle settings": "設定を切り替え", "Toggle sidebar": "サイドバーを切り替え", "Token": "", "Tokens To Keep On Context Refresh (num_keep)": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool updated successfully": "", "Tools": "ツール", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Top K": "トップ K", "Top P": "トップ P", "Transformers": "", "Trouble accessing Ollama?": "Ollama へのアクセスに問題がありますか？", "TTS Model": "TTSモデル", "TTS Settings": "TTS 設定", "TTS Voice": "TTSボイス", "Type": "種類", "Type Hugging Face Resolve (Download) URL": "Hugging Face Resolve (ダウンロード) URL を入力してください", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Update": "", "Update and Copy Link": "リンクの更新とコピー", "Update for the latest features and improvements.": "", "Update password": "パスワードを更新", "Updated": "", "Updated at": "", "Updated At": "", "Upload": "アップロード", "Upload a GGUF model": "GGUF モデルをアップロード", "Upload directory": "アップロードディレクトリ", "Upload files": "アップロードファイル", "Upload Files": "ファイルのアップロード", "Upload Pipeline": "アップロードパイプライン", "Upload Progress": "アップロードの進行状況", "URL": "", "URL Mode": "URL モード", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "#を入力するとナレッジベースを参照することが出来ます。", "Use Gravatar": "Gravatar を使用する", "Use groups to group your users and assign permissions.": "", "Use Initials": "初期値を使用する", "use_mlock (Ollama)": "", "use_mmap (Ollama)": "", "user": "ユーザー", "User": "", "User location successfully retrieved.": "", "Username": "", "Users": "ユーザー", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "活用", "Valid time units:": "有効な時間単位:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "変数", "variable to have them replaced with clipboard content.": "クリップボードの内容に置き換える変数。", "Version": "バージョン", "Version {{selectedVersion}} of {{totalVersions}}": "", "Very bad": "", "View Replies": "", "Visibility": "", "Voice": "ボイス", "Voice Input": "", "Warning": "警告", "Warning:": "警告:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "警告: 埋め込みモデルを更新または変更した場合は、すべてのドキュメントを再インポートする必要があります。", "Web": "ウェブ", "Web API": "ウェブAPI", "Web Loader Settings": "Web 読み込み設定", "Web Search": "ウェブ検索", "Web Search Engine": "ウェブ検索エンジン", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI 設定", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Welcome, {{name}}!": "", "What are you trying to achieve?": "", "What are you working on?": "", "What didn't you like about this response?": "", "What’s New in": "新機能", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whisper (Local)": "", "Widescreen Mode": "ワイドスクリーンモード", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "", "Workspace": "ワークスペース", "Workspace Permissions": "", "Write a prompt suggestion (e.g. Who are you?)": "プロンプトの提案を書いてください (例: あなたは誰ですか？)", "Write a summary in 50 words that summarizes [topic or keyword].": "[トピックまたはキーワード] を要約する 50 語の概要を書いてください。", "Write something...": "", "Write your model template content here": "", "Yesterday": "昨日", "You": "あなた", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You have no archived conversations.": "これまでにアーカイブされた会話はありません。", "You have shared this chat": "このチャットを共有しました", "You're a helpful assistant.": "あなたは有能なアシスタントです。", "Your account status is currently pending activation.": "あなたのアカウント状態は現在登録認証待ちです。", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "YouTube", "Youtube Loader Settings": "YouTubeローダー設定(日本語はja)"}