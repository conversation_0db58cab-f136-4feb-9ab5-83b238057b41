{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' oder '-1' für keine Ablaufzeit.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(z. B. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(z. B. `sh webui.sh --api`)", "(latest)": "(neueste)", "{{ models }}": "{{ <PERSON><PERSON> }}", "{{COUNT}} Replies": "", "{{user}}'s Chats": "{{user}}s Unterhaltungen", "{{webUIName}} Backend Required": "{{webUIName}}-<PERSON><PERSON> <PERSON><PERSON>", "*Prompt node ID(s) are required for image generation": "*Prompt-Node-ID(s) sind für die Bildgenerierung erforderlich", "A new version (v{{LATEST_VERSION}}) is now available.": "Eine neue Version (v{{LATEST_VERSION}}) ist jetzt verfügbar.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Aufgabenmodelle können Unterhaltungstitel oder Websuchanfragen generieren.", "a user": "ein <PERSON>", "About": "<PERSON><PERSON>", "Access": "Zugang", "Access Control": "Zugangskontrolle", "Accessible to all users": "<PERSON><PERSON><PERSON> alle Benutzer zugä<PERSON>", "Account": "Ko<PERSON>", "Account Activation Pending": "Kontoaktivierung ausstehend", "Actions": "Aktionen", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Aktivieren Sie diesen Befehl, indem Sie \"/{{COMMAND}}\" in die Chat-Eingabe eingeben.", "Active Users": "Aktive Benutzer", "Add": "Hinzufügen", "Add a model ID": "Modell-ID hinzufügen", "Add a short description about what this model does": "Fügen Sie eine kurze Beschreibung über dieses Modell hinzu", "Add a tag": "Tag hinzufügen", "Add Arena Model": "Arena-<PERSON><PERSON> hinzufügen", "Add Connection": "Verbindung hinzufügen", "Add Content": "Inhalt hinzufügen", "Add content here": "Inhalt hier hinzufügen", "Add custom prompt": "Benutzerdefinierten Prompt hinzufügen", "Add Files": "<PERSON><PERSON>", "Add Group": "Gruppe hinzufügen", "Add Memory": "Erinnerung hinzufügen", "Add Model": "<PERSON><PERSON>", "Add Reaction": "", "Add Tag": "Tag hinzufügen", "Add Tags": "Tags hinzufügen", "Add text content": "Textinhalt hinzufügen", "Add User": "Benutzer hinzufügen", "Add User Group": "Benutzergruppe hinzufügen", "Additional query terms to append to all searches (e.g. site:wikipedia.org)": "", "Adjusting these settings will apply changes universally to all users.": "Das Anpassen dieser Einstellungen wird Änderungen universell auf alle Benutzer anwenden.", "admin": "Administrator", "Admin": "Administrator", "Admin Panel": "Administrationsbereich", "Admin Settings": "Administrationsbereich", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Administratoren haben jederzeit Zugriff auf alle Werkzeuge. Benutzer können im Arbeitsbereich zugewiesen.", "Advanced Parameters": "Erweiterte Parameter", "Advanced Params": "Erweiterte Parameter", "All Documents": "Alle Dokumente", "All models deleted successfully": "Alle Modelle erfolgreich gelöscht", "Allow Chat Delete": "Löschen von Unterhaltungen erlauben", "Allow Chat Deletion": "Löschen von Unterhaltungen erlauben", "Allow Chat Edit": "Bearbeiten von Unterhaltungen erlauben", "Allow File Upload": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "Allow non-local voices": "Nicht-lokale Stimmen erlauben", "Allow Temporary Chat": "Temporäre Unterhaltungen erlauben", "Allow User Location": "Standort freigeben", "Allow Voice Interruption in Call": "Unterbrechung durch Stimme im Anruf zulassen", "Allowed Endpoints": "", "Already have an account?": "Haben Sie bereits einen Account?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out. (Default: 0.0)": "Alternative zu top_p und zielt darauf ab, ein Gleichgewicht zwischen Qualität und Vielfalt zu gewährleisten. Der Parameter p repräsentiert die Mindestwahrscheinlichkeit für ein Token, um berücksichtigt zu werden, relativ zur Wahrscheinlichkeit des wahrscheinlichsten Tokens. Zum Beispiel, bei p=0.05 und das wahrscheinlichste Token hat eine Wahrscheinlichkeit von 0.9, werden Logits mit einem Wert von weniger als 0.045 herausgefiltert. (Standard: 0.0)", "an assistant": "ein Assistent", "and": "und", "and {{COUNT}} more": "und {{COUNT}} mehr", "and create a new shared link.": "und erstellen Sie einen neuen freigegebenen Link.", "api": "", "API Base URL": "API-Basis-URL", "API Key": "API-Schlüssel", "API Key created.": "API-Schlüssel erstellt.", "API Key Endpoint Restrictions": "", "API keys": "API-Schlüssel", "Application DN": "Anwendungs-DN", "Application DN Password": "Anwendungs-DN-Passwort", "applies to all users with the \"user\" role": "gilt für alle Benutzer mit der Rolle \"Benutzer\"", "April": "April", "Archive": "Archivieren", "Archive All Chats": "Alle Unterhaltungen archivieren", "Archived Chats": "Archivierte Unterhaltungen", "archived-chat-export": "archivierter-chat-export", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "Sind <PERSON> sicher, dass Sie alle archivierten Unterhaltungen wiederherstellen möchten?", "Are you sure?": "Sind Sie sicher?", "Arena Models": "Arena-Modelle", "Artifacts": "Artefakte", "Ask a question": "<PERSON><PERSON><PERSON> eine Fr<PERSON>", "Assistant": "Assistent", "Attach file": "<PERSON><PERSON>", "Attribute for Username": "Attribut für Benutzername", "Audio": "Audio", "August": "August", "Authenticate": "Authentifizieren", "Auto-Copy Response to Clipboard": "Antwort automatisch in die Zwischenablage kopieren", "Auto-playback response": "Antwort automatisch abspielen", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111-API-Authentifizierungszeichenfolge", "AUTOMATIC1111 Base URL": "AUTOMATIC1111-Basis-URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111-Basis-URL ist erforderlich.", "Available list": "Verfügbare Liste", "available!": "Verfügbar!", "Azure AI Speech": "Azure AI Speech", "Azure Region": "Azure-Region", "Back": "Zurück", "Bad": "", "Bad Response": "Schlechte Antwort", "Banners": "Banner", "Base Model (From)": "<PERSON><PERSON><PERSON><PERSON> (From)", "Batch Size (num_batch)": "Stapelgröße (num_batch)", "before": "bereits geteilt", "Beta": "", "Bing Search V7 Endpoint": "Bing Search V7-Endpunkt", "Bing Search V7 Subscription Key": "Bing Search V7-Abonnement-Sc<PERSON><PERSON>ssel", "Brave Search API Key": "Brave Search API-Schlüssel", "By {{name}}": "Von {{name}}", "Bypass SSL verification for Websites": "SSL-Überprüfung für Webseiten umgehen", "Call": "Anrufen", "Call feature is not supported when using Web STT engine": "Die Anruffunktion wird nicht unterstützt, wenn die Web-STT-Engine verwendet wird.", "Camera": "<PERSON><PERSON><PERSON>", "Cancel": "Abbrechen", "Capabilities": "Fähigkeiten", "Capture": "", "Certificate Path": "Zertifikatpfad", "Change Password": "Passwort ändern", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Neue Wege beschreiten", "chat": "", "Chat": "Gesprä<PERSON>", "Chat Background Image": "Hintergrundbild des Unterhaltungsfensters", "Chat Bubble UI": "Chat Bubble UI", "Chat Controls": "Chat-Steuerung", "Chat direction": "Textrichtung", "Chat Overview": "Unterhaltungsübersicht", "Chat Permissions": "Unterhaltungsberechtigungen", "Chat Tags Auto-Generation": "Automatische Generierung von Unterhaltungstags", "Chats": "Unterhaltungen", "Check Again": "Erneut überprüfen", "Check for updates": "<PERSON>ch Updates suchen", "Checking for updates...": "Sucht nach Updates...", "Choose a model before saving...": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> e<PERSON> Modell, bevor <PERSON>...", "Chunk Overlap": "Blocküberlappung", "Chunk Params": "Blockparameter", "Chunk Size": "Blockgröße", "Ciphers": "Verschlüsselungen", "Citation": "Zitate", "Clear memory": "Alle Erinnerungen entfernen", "click here": "hier klicken", "Click here for filter guides.": "Klicken Sie hier für Filteranleitungen.", "Click here for help.": "<PERSON>licken Sie hier für Hilfe.", "Click here to": "<PERSON><PERSON><PERSON> hier, um", "Click here to download user import template file.": "<PERSON><PERSON><PERSON> hier, um die Vorlage für den Benutzerimport herunterzuladen.", "Click here to learn more about faster-whisper and see the available models.": "<PERSON><PERSON><PERSON> Si<PERSON> hier, um mehr über faster-whisper zu erfahren und die verfügbaren Modelle zu sehen.", "Click here to select": "<PERSON>licke Sie zum Auswählen hier", "Click here to select a csv file.": "Klicken Sie zum Auswählen einer CSV-<PERSON><PERSON> hier.", "Click here to select a py file.": "<PERSON>licken Si<PERSON> zum Auswählen einer py-Datei hier.", "Click here to upload a workflow.json file.": "Klicken sie zum Hochladen einer workflow.json-<PERSON><PERSON> hier.", "click here.": "hier klicken.", "Click on the user role button to change a user's role.": "<PERSON>licken Si<PERSON> auf die Benutzerrolle, um sie zu ändern.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Schreibberechtigung für die Zwischenablage verweigert. Bitte überprüfen Sie Ihre Browsereinstellungen, um den erforderlichen Zugriff zu erlauben.", "Clone": "Klonen", "Close": "Schließen", "Code execution": "Codeausführung", "Code formatted successfully": "Code erfolgreich formatiert", "Collection": "Kollektion", "Color": "Farbe", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI-Basis-URL", "ComfyUI Base URL is required.": "ComfyUI-Basis-URL wird <PERSON><PERSON><PERSON><PERSON>.", "ComfyUI Workflow": "ComfyUI-Workflow", "ComfyUI Workflow Nodes": "ComfyUI-Workflow-Knoten", "Command": "<PERSON><PERSON><PERSON>", "Completions": "Vervollständigungen", "Concurrent Requests": "<PERSON><PERSON>hl gleichzeitiger Anfragen", "Configure": "Konfigurieren", "Configure Models": "", "Confirm": "Bestätigen", "Confirm Password": "Passwort bestätigen", "Confirm your action": "Bestätigen Sie Ihre Aktion.", "Confirm your new password": "", "Connections": "Verbindungen", "console": "", "Contact Admin for WebUI Access": "Kontaktieren Sie den Administrator für den Zugriff auf die Weboberfläche", "Content": "Info", "Content Extraction": "Inhaltsextraktion", "Context Length": "Kontextlänge", "Continue Response": "Antwort fortsetzen", "Continue with {{provider}}": "Mit {{provider}} fortfahren", "Continue with Email": "<PERSON><PERSON> <PERSON>", "Continue with LDAP": "Mit LDAP fortfahren", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Kontrollieren Sie, wie Nachrichtentext für TTS-Anfragen aufgeteilt wird. 'Punctuation' teilt in Sätze auf, 'paragraphs' teilt in Abs<PERSON>ze auf und 'none' behält die Nachricht als einzelnen String.", "Controls": "Steuerung", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text. (Default: 5.0)": "Kontrolliert das Gleichgewicht zwischen Kohärenz und Vielfalt des Ausgabetextes. Ein niedrigerer Wert führt zu fokussierterem und kohärenterem Text. (Standard: 5.0)", "Copied": "<PERSON><PERSON><PERSON>", "Copied shared chat URL to clipboard!": "Freigabelink in die Zwischenablage kopiert!", "Copied to clipboard": "In die Zwischenablage kopiert", "Copy": "<PERSON><PERSON><PERSON>", "Copy last code block": "Letzten Codeblock kopieren", "Copy last response": "Letzte Antwort kopieren", "Copy Link": "<PERSON>", "Copy to clipboard": "In die Zwischenablage kopieren", "Copying to clipboard was successful!": "Das Kopieren in die Zwischenablage war erfolgreich!", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create a knowledge base": "Wissensspeicher erstellen", "Create a model": "<PERSON><PERSON>", "Create Account": "<PERSON><PERSON> er<PERSON>", "Create Admin Account": "Administrator-Account er<PERSON><PERSON>", "Create Channel": "", "Create Group": "Gruppe er<PERSON>llen", "Create Knowledge": "Wissen erstellen", "Create new key": "Neuen Schlüssel erstellen", "Create new secret key": "Neuen API-Schlüssel erstellen", "Created at": "Erstellt am", "Created At": "Erstellt am", "Created by": "<PERSON><PERSON><PERSON><PERSON> von", "CSV Import": "CSV-Import", "Current Model": "Aktuelles Modell", "Current Password": "Aktuelles Passwort", "Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Dark": "<PERSON><PERSON><PERSON>", "Database": "Datenbank", "December": "Dezember", "Default": "Standard", "Default (Open AI)": "Standard (Open AI)", "Default (SentenceTransformers)": "Standard (SentenceTransformers)", "Default Model": "Standardmodell", "Default model updated": "Standardmodell aktualisiert", "Default Models": "", "Default permissions": "Standardberechtigungen", "Default permissions updated successfully": "Standardberechtigungen erfolgreich aktualisiert", "Default Prompt Suggestions": "Prompt-Vorschläge", "Default to 389 or 636 if TLS is enabled": "Standardmä<PERSON><PERSON> auf 389 oder 636 setzen, wenn TLS aktiviert ist", "Default to ALL": "Standardmäßig auf ALLE setzen", "Default User Role": "Standardbenutzerrolle", "Delete": "Löschen", "Delete a model": "Ein Modell löschen", "Delete All Chats": "Alle Unterhaltungen löschen", "Delete All Models": "Alle Modelle löschen", "Delete chat": "Unterhaltung löschen", "Delete Chat": "Unterhaltung löschen", "Delete chat?": "Unterhaltung löschen?", "Delete folder?": "Ordner löschen?", "Delete function?": "Funktion löschen?", "Delete Message": "", "Delete prompt?": "Prompt löschen?", "delete this link": "diesen Link löschen", "Delete tool?": "Werkzeug löschen?", "Delete User": "Benutzer löschen", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} gelöscht", "Deleted {{name}}": "{{name}} <PERSON><PERSON><PERSON><PERSON>", "Deleted User": "<PERSON>utz<PERSON> gelöscht", "Describe your knowledge base and objectives": "Beschreibe deinen Wissensspeicher und deine Ziele", "Description": "Beschreibung", "Disabled": "Deaktiviert", "discover": "", "Discover a function": "Entdecken Sie weitere Funktionen", "Discover a model": "Entdecken Sie weitere Modelle", "Discover a prompt": "Entdecken Sie weitere Prompts", "Discover a tool": "Entdecken Sie weitere Werkzeuge", "Discover wonders": "Entdecken Si<PERSON>", "Discover, download, and explore custom functions": "Entdecken und beziehen Sie benutzerdefinierte Funktionen", "Discover, download, and explore custom prompts": "Ent<PERSON><PERSON>n und beziehen Sie benutzerdefinierte Prompts", "Discover, download, and explore custom tools": "Entdecken und beziehen Sie benutzerdefinierte Werkzeuge", "Discover, download, and explore model presets": "Entdecken und beziehen Sie benutzerdefinierte Modellvorlagen", "Dismissible": "ausblendbar", "Display": "Anzeigen", "Display Emoji in Call": "Emojis im Anruf anzeigen", "Display the username instead of You in the Chat": "<PERSON>l \"Sie\" durch Ihren Benutzernamen ersetzt werden?", "Displays citations in the response": "<PERSON><PERSON><PERSON> in der Antwort an", "Dive into knowledge": "<PERSON><PERSON> Si<PERSON> in das Wissen ein", "Do not install functions from sources you do not fully trust.": "Installieren Sie keine Funktionen aus Quellen, denen Si<PERSON> nicht vollständig vertrauen.", "Do not install tools from sources you do not fully trust.": "Installieren Sie keine Werkzeuge aus Quellen, denen Si<PERSON> nicht vollständig vertrauen.", "Document": "Dokument", "Documentation": "Dokumentation", "Documents": "Dokumente", "does not make any external connections, and your data stays securely on your locally hosted server.": "stellt keine externen Verbindungen her, und Ihre Daten bleiben sicher auf Ihrem lokal gehosteten Server.", "Don't have an account?": "Haben Si<PERSON> noch kein Benutzerkonto?", "don't install random functions from sources you don't trust.": "installieren Sie keine Funktionen aus Quellen, denen Si<PERSON> nicht vertrauen.", "don't install random tools from sources you don't trust.": "installieren Sie keine Werkzeuge aus Quellen, denen Si<PERSON> nicht vertrauen.", "Done": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Exportieren", "Download canceled": "Exportierung abgebrochen", "Download Database": "Datenbank exportieren", "Drag and drop a file to upload or select a file to view": "<PERSON><PERSON><PERSON> Sie eine Datei zum Hochladen oder wählen Sie eine Datei zum Anzeigen aus", "Draw": "<PERSON><PERSON><PERSON><PERSON>", "Drop any files here to add to the conversation": "<PERSON><PERSON>hen Sie beliebige Dateien hierher, um sie der Unterhaltung hinzuzufügen", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "z. B. '30s','10m'. Gültige Zeiteinheiten sind 's', 'm', 'h'.", "e.g. A filter to remove profanity from text": "z. B<PERSON> Filter, um Schimpfwörter aus Text zu entfernen", "e.g. My Filter": "<PERSON><PERSON> <PERSON><PERSON>", "e.g. My Tools": "z. B. Meine Werkzeuge", "e.g. my_filter": "z<PERSON> <PERSON><PERSON> mein_filter", "e.g. my_tools": "z. B. meine_werkzeuge", "e.g. Tools for performing various operations": "z. B. Werkzeuge für verschiedene Operationen", "Edit": "<PERSON><PERSON><PERSON>", "Edit Arena Model": "Arena-<PERSON><PERSON> bear<PERSON>ten", "Edit Channel": "", "Edit Connection": "Verbindung bearbeiten", "Edit Default Permissions": "Standardberechtigungen bearbeiten", "Edit Memory": "Erinnerungen bearbeiten", "Edit User": "<PERSON><PERSON><PERSON> bearbeiten", "Edit User Group": "Benutzergruppe bearbeiten", "ElevenLabs": "ElevenLabs", "Email": "E-Mail", "Embark on adventures": "Abenteuer erleben", "Embedding Batch Size": "Embedding-Stapelgröße", "Embedding Model": "Embedding-Modell", "Embedding Model Engine": "Embedding-Modell-Engine", "Embedding model set to \"{{embedding_model}}\"": "Embedding-Modell auf \"{{embedding_model}}\" gesetzt", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Community Sharing": "Community-Freigabe aktivieren", "Enable Google Drive": "", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Aktiviere Memory Locking (mlock), um zu verhindern, dass Modelldaten aus dem RAM ausgelagert werden. Diese Option sperrt die Arbeitsseiten des Modells im RAM, um sicherzustellen, dass sie nicht auf die Festplatte ausgelagert werden. Dies kann die Leistung verbessern, indem Page Faults vermieden und ein schneller Datenzugriff sichergestellt werden.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Aktiviere Memory Mapping (mmap), um Modelldaten zu laden. Diese Option ermöglicht es dem System, den Festplattenspeicher als Erweiterung des RAM zu verwenden, indem Festplattendateien so behandelt werden, als ob sie im RAM wären. Dies kann die Modellleistung verbessern, indem ein schnellerer Datenzugriff ermöglicht wird. Es kann jedoch nicht auf allen Systemen korrekt funktionieren und einen erheblichen Teil des Festplattenspeichers beanspruchen.", "Enable Message Rating": "Nachrichtenbewertung aktivieren", "Enable Mirostat sampling for controlling perplexity. (Default: 0, 0 = Disabled, 1 = Mirostat, 2 = Mirostat 2.0)": "Mirostat Sampling zur Steuerung der Perplexität aktivieren. (Standard: 0, 0 = Deaktiviert, 1 = Mirostat, 2 = Mirostat 2.0)", "Enable New Sign Ups": "Registrierung erlauben", "Enable Web Search": "Websuche aktivieren", "Enabled": "Aktiviert", "Engine": "Engine", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "<PERSON><PERSON><PERSON>, dass Ihre CSV-Datei 4 Spalten in dieser Reihenfolge enthält: Name, E-Mail, Passwort, Rolle.", "Enter {{role}} message here": "<PERSON><PERSON><PERSON> die {{role}}-<PERSON><PERSON><PERSON><PERSON> hier ein", "Enter a detail about yourself for your LLMs to recall": "<PERSON><PERSON><PERSON> Si<PERSON> ein Detail über sich selbst ein, das Ihre Sprachmodelle (LLMs) sich merken sollen", "Enter api auth string (e.g. username:password)": "Geben Sie die API-Authentifizierungszeichenfolge ein (z. B. Benutzername:Passwort)", "Enter Application DN": "Geb<PERSON> Sie die Anwendungs-DN ein", "Enter Application DN Password": "Geben Sie das Anwendungs-DN-Passwort ein", "Enter Bing Search V7 Endpoint": "<PERSON><PERSON><PERSON> Sie den Bing Search V7-<PERSON>punkt ein", "Enter Bing Search V7 Subscription Key": "<PERSON>eben Sie den Bing Search V7-Abonnement-Schl<PERSON><PERSON> ein", "Enter Brave Search API Key": "Geben Sie den Brave Search API-Schlüssel ein", "Enter certificate path": "Geben Sie den Zertifikatpfad ein", "Enter CFG Scale (e.g. 7.0)": "Geben Sie die CFG-Skala ein (z. B. 7.0)", "Enter Chunk Overlap": "<PERSON><PERSON><PERSON> Si<PERSON> die Blocküberlappung ein", "Enter Chunk Size": "<PERSON><PERSON><PERSON> Si<PERSON> die Blockgröße ein", "Enter description": "Geben Sie eine Beschreibung ein", "Enter Github Raw URL": "<PERSON><PERSON><PERSON> Sie die Github Raw-URL ein", "Enter Google PSE API Key": "Geben Sie den Google PSE-API-Schlüssel ein", "Enter Google PSE Engine Id": "<PERSON>eben Sie die Google PSE-Engine-ID ein", "Enter Image Size (e.g. 512x512)": "<PERSON>eben Sie die Bildgröße ein (z. B. 512x512)", "Enter Jina API Key": "Geben Sie den Jina-API-Schl<PERSON>ssel ein", "Enter Kagi Search API Key": "", "Enter language codes": "Geben Sie die Sprachcodes ein", "Enter Model ID": "<PERSON><PERSON><PERSON> Sie die Modell-ID ein", "Enter model tag (e.g. {{modelTag}})": "<PERSON><PERSON><PERSON> Sie den Model-Tag ein", "Enter Mojeek Search API Key": "Geben Sie den Mojeek Search API-Schlüssel ein", "Enter name or email": "", "Enter Number of Steps (e.g. 50)": "<PERSON>eb<PERSON> Sie die Anzahl an Schritten ein (z. B. 50)", "Enter proxy URL (e.g. **************************:port)": "", "Enter Sampler (e.g. Euler a)": "<PERSON>eb<PERSON> Sie den Sampler ein (z. B. Euler a)", "Enter Scheduler (e.g. Karras)": "<PERSON><PERSON><PERSON> e<PERSON> (z. B. <PERSON>)", "Enter Score": "<PERSON><PERSON><PERSON>", "Enter SearchApi API Key": "Geben Sie den SearchApi-API-Schlüssel ein", "Enter SearchApi Engine": "<PERSON><PERSON><PERSON> Sie die SearchApi-Engine ein", "Enter Searxng Query URL": "Geben Sie die Searxng-Abfrage-URL ein", "Enter Seed": "<PERSON><PERSON><PERSON> Si<PERSON> den Seed ein", "Enter Serper API Key": "Geben Sie den Serper-API-Schlüssel ein", "Enter Serply API Key": "<PERSON><PERSON><PERSON> den", "Enter Serpstack API Key": "Geben Sie den Serpstack-API-Schlüssel ein", "Enter server host": "<PERSON><PERSON><PERSON>-Host ein", "Enter server label": "<PERSON><PERSON><PERSON> Sie das Server-Label ein", "Enter server port": "<PERSON>eben Sie den Server-Port ein", "Enter stop sequence": "Stop-Sequenz eingeben", "Enter system prompt": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "Enter Tavily API Key": "Geben Sie den Tavily-API-Schlüssel ein", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter Tika Server URL": "<PERSON><PERSON><PERSON> die Tika-Server-URL ein", "Enter Top K": "<PERSON><PERSON><PERSON> Sie Top K ein", "Enter URL (e.g. http://127.0.0.1:7860/)": "<PERSON><PERSON><PERSON> Sie die URL ein (z. B. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "<PERSON>eb<PERSON> Sie die URL ein (z. B. http://localhost:11434)", "Enter your current password": "", "Enter Your Email": "Geben Sie Ihre E-Mail-Adresse ein", "Enter Your Full Name": "Geben Sie Ihren vollständigen Namen ein", "Enter your message": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht ein", "Enter your new password": "", "Enter Your Password": "Geben Sie Ihr Passwort ein", "Enter your prompt": "", "Enter Your Role": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Rolle ein", "Enter Your Username": "<PERSON><PERSON>en Sie Ihren Benutzernamen ein", "Enter your webhook URL": "", "Error": "<PERSON><PERSON>", "ERROR": "FEHLER", "Error accessing Google Drive: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "<PERSON><PERSON>", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Beispiel: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Beispiel: ALL", "Example: ou=users,dc=foo,dc=example": "Beispiel: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Beispiel: sAMAccountName or uid or userPrincipalName", "Exclude": "Ausschließen", "Experimental": "Experimentell", "Explore the cosmos": "Erforschen Sie das Universum", "Export": "Exportieren", "Export All Archived Chats": "Alle archivierten Unterhaltungen exportieren", "Export All Chats (All Users)": "Alle Unterhaltungen exportieren (alle Benutzer)", "Export chat (.json)": "Unterhaltung exportieren (.json)", "Export Chats": "Unterhaltungen exportieren", "Export Config to JSON File": "Exportiere Konfiguration als JSON-Datei", "Export Functions": "Funktionen exportieren", "Export Models": "Modelle exportieren", "Export Presets": "Voreinstellungen exportieren", "Export Prompts": "Prompts exportieren", "Export to CSV": "Als CSV exportieren", "Export Tools": "Werkzeuge exportieren", "External Models": "Externe Modelle", "Extra Search Query Params": "", "Extremely bad": "", "Failed to add file.": "Fehler beim Hinzufügen der Datei.", "Failed to create API Key.": "Fehler beim Erstellen des API-Schlüssels.", "Failed to read clipboard contents": "Fehler beim Abruf der Zwischenablage", "Failed to save models configuration": "", "Failed to update settings": "Fehler beim Aktualisieren der Einstellungen", "February": "<PERSON><PERSON><PERSON>", "Feedback History": "Fe<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Feedbacks": "Feedbacks", "File": "<PERSON><PERSON>", "File added successfully.": "<PERSON>i erfolgreich hinzugefügt.", "File content updated successfully.": "Dateiinhalt erfolgreich aktualisiert.", "File Mode": "Datei-Modus", "File not found.": "Datei nicht gefunden.", "File removed successfully.": "Datei erfolgreich entfernt.", "File size should not exceed {{maxSize}} MB.": "<PERSON>i darf nicht größer als {{maxSize}} MB sein.", "File uploaded successfully": "", "Files": "<PERSON><PERSON>", "Filter is now globally disabled": "Filter ist jetzt global deaktiviert", "Filter is now globally enabled": "Filter ist jetzt global aktiviert", "Filters": "Filter", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Fingerabdruck-Spoofing erkannt: Initialen können nicht als Avatar verwendet werden. Standard-Avatar wird verwendet.", "Fluidly stream large external response chunks": "Nahtlose Übertragung großer externer Antwortabschnitte", "Focus chat input": "Chat-Eingabe fokussieren", "Folder deleted successfully": "Ordner erfolgreich <PERSON>t", "Folder name cannot be empty": "Ordnern<PERSON> darf nicht leer sein", "Folder name cannot be empty.": "Ordnername darf nicht leer sein.", "Folder name updated successfully": "Ordnername erfolgreich aktualisiert", "Forge new paths": "Neue Wege beschreiten", "Form": "Formular", "Format your variables using brackets like this:": "Formatieren Sie Ihre Variablen mit Klammern, wie hier:", "Frequency Penalty": "Frequenzstrafe", "Function": "Funktion", "Function created successfully": "Funktion erfolgreich erstellt", "Function deleted successfully": "Funktion erfolgreich <PERSON>", "Function Description": "Funktionsbeschreibung", "Function ID": "Funktions-ID", "Function is now globally disabled": "Die Funktion ist jetzt global deaktiviert", "Function is now globally enabled": "Die Funktion ist jetzt global aktiviert", "Function Name": "Funktionsname", "Function updated successfully": "Funktion erfolgreich aktualisiert", "Functions": "Funktionen", "Functions allow arbitrary code execution": "Funktionen ermöglichen die Ausführung beliebigen Codes", "Functions allow arbitrary code execution.": "Funktionen ermöglichen die Ausführung beliebigen Codes.", "Functions imported successfully": "Funktionen erfolgreich importiert", "General": "Allgemein", "General Settings": "Allgemeine Einstellungen", "Generate Image": "<PERSON>ild er<PERSON>", "Generating search query": "Suchan<PERSON>ge wird erstellt", "Get started": "Loslegen", "Get started with {{WEBUI_NAME}}": "Loslegen mit {{WEBUI_NAME}}", "Global": "Global", "Good Response": "Gute Antwort", "Google Drive": "", "Google PSE API Key": "Google PSE-API-Schlüssel", "Google PSE Engine Id": "Google PSE-Engine-ID", "Group created successfully": "Gruppe erfolgreich erstellt", "Group deleted successfully": "Gruppe erfolgreich <PERSON>", "Group Description": "Gruppenbeschreibung", "Group Name": "Gruppenname", "Group updated successfully": "Gruppe erfolgreich aktualisiert", "Groups": "Gruppen", "h:mm a": "h:mm a", "Haptic Feedback": "Haptisches Feedback", "Harmful or offensive": "", "has no conversations.": "hat keine Unterhaltungen.", "Hello, {{name}}": "<PERSON><PERSON>, {{name}}", "Help": "<PERSON><PERSON><PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "<PERSON><PERSON><PERSON>, die beste Community-Bestenliste zu erstellen, indem Sie Ihren Feedback-<PERSON><PERSON><PERSON><PERSON> te<PERSON>!", "Hex Color": "Hex-Farbe", "Hex Color - Leave empty for default color": "Hex-Farbe - <PERSON><PERSON> lassen für Standardfarbe", "Hide": "Verbergen", "Host": "Host", "How can I help you today?": "Wie kann ich Ihnen heute helfen?", "How would you rate this response?": "Wie würden Sie diese Antwort bewerten?", "Hybrid Search": "<PERSON><PERSON>", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "<PERSON>ch bestätige, dass ich gelesen habe und die Auswirkungen meiner Aktion verstehe. Mir sind die Risiken bewusst, die mit der Ausführung beliebigen Codes verbunden sind, und ich habe die Vertrauenswürdigkeit der Quelle überprüft.", "ID": "ID", "Ignite curiosity": "Neugier entfachen", "Image Compression": "", "Image Generation (Experimental)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (experimentell)", "Image Generation Engine": "Bildgenerierungs-Engine", "Image Max Compression Size": "", "Image Settings": "Bildeinstellungen", "Images": "Bilder", "Import Chats": "Unterhaltungen importieren", "Import Config from JSON File": "Konfiguration aus JSON-Datei importieren", "Import Functions": "Funktionen importieren", "Import Models": "Modelle importieren", "Import Presets": "Voreinstellungen importieren", "Import Prompts": "Prompts importieren", "Import Tools": "Werkzeuge importieren", "Include": "Einschließen", "Include `--api-auth` flag when running stable-diffusion-webui": "<PERSON><PERSON>gen Sie beim Ausführen von stable-diffusion-webui die Option `--api-auth` hinzu", "Include `--api` flag when running stable-diffusion-webui": "<PERSON><PERSON>gen Sie beim Ausführen von stable-diffusion-webui die Option `--api` hinzu", "Incomplete response": "", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive. (Default: 0.1)": "<PERSON><PERSON><PERSON><PERSON><PERSON>, wie schnell der Algorithmus auf Feedback aus dem generierten Text reagiert. Eine niedrigere Lernrate führt zu langsameren Anpassungen, während eine höhere Lernrate den Algorithmus reaktionsschneller macht. (Standard: 0.1)", "Info": "Info", "Input commands": "Eingabebefehle", "Install from Github URL": "Installiere von der Github-URL", "Instant Auto-Send After Voice Transcription": "Spracherkennung direkt absenden", "Interface": "Benutzeroberfläche", "Invalid file format.": "Ungültiges Dateiformat.", "Invalid Tag": "Ungültiger Tag", "is typing...": "", "January": "<PERSON><PERSON><PERSON>", "Jina API Key": "Jina-API-Schlüssel", "join our Discord for help.": "Treten Si<PERSON> unserem Discord bei, um Hilfe zu erhalten.", "JSON": "JSON", "JSON Preview": "JSON-Vorschau", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "JWT Expiration": "JWT-A<PERSON>uf", "JWT Token": "JWT-Token", "Kagi Search API Key": "", "Keep Alive": "Verbindung aufrechterhalten", "Key": "Schlüssel", "Keyboard shortcuts": "Tastenkombinationen", "Knowledge": "Wissen", "Knowledge Access": "Wissenszugriff", "Knowledge created successfully.": "Wissen erfolgreich erstellt.", "Knowledge deleted successfully.": "Wissen erfolgreich gelöscht.", "Knowledge reset successfully.": "Wissen erfolgreich zurückgesetzt.", "Knowledge updated successfully": "Wissen erfolgreich aktualisiert", "Label": "Label", "Landing Page Mode": "Startseitenmodus", "Language": "<PERSON><PERSON><PERSON>", "Last Active": "Zuletzt aktiv", "Last Modified": "Zuletzt bearbeitet", "Last reply": "", "Latest users": "", "LDAP": "LDAP", "LDAP server updated": "LDAP-Server aktualisiert", "Leaderboard": "Bestenlist<PERSON>", "Leave empty for unlimited": "<PERSON><PERSON> lassen für unbegrenzt", "Leave empty to include all models from \"{{URL}}/api/tags\" endpoint": "<PERSON><PERSON>, um alle Modelle vom \"{{URL}}/api/tags\"-Endpunkt einzuschließen", "Leave empty to include all models from \"{{URL}}/models\" endpoint": "<PERSON><PERSON>, um alle Modelle vom \"{{URL}}/models\"-Endpunkt einzuschließen", "Leave empty to include all models or select specific models": "<PERSON><PERSON>, um alle Modelle einzuschließen oder spezifische Modelle auszuwählen", "Leave empty to use the default prompt, or enter a custom prompt": "<PERSON><PERSON>, um den Standardprompt zu verwenden, oder geben Sie einen benutzerdefinierten Prompt ein", "Light": "Hell", "Listening...": "<PERSON><PERSON><PERSON> zu...", "Local": "<PERSON><PERSON>", "Local Models": "Lokale Modelle", "Lost": "Verloren", "LTR": "LTR", "Made by OpenWebUI Community": "<PERSON> der OpenWebUI-Community", "Make sure to enclose them with": "Umschließe Variablen mit", "Make sure to export a workflow.json file as API format from ComfyUI.": "<PERSON><PERSON><PERSON>, dass sie eine workflow.json-Datei im API-Format von ComfyUI exportieren.", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage Arena Models": "Arena-<PERSON><PERSON> ver<PERSON>", "Manage Ollama": "<PERSON><PERSON><PERSON> verwalten", "Manage Ollama API Connections": "Ollama-API-Verbindungen verwalten", "Manage OpenAI API Connections": "OpenAI-API-Verbindungen verwalten", "Manage Pipelines": "Pipelines verwalten", "March": "<PERSON><PERSON><PERSON>", "Max Tokens (num_predict)": "Maximale Tokenanzahl (num_predict)", "Max Upload Count": "Maximale Anzahl der Uploads", "Max Upload Size": "Maximale Uploadgröße", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Es können maximal 3 Modelle gleichzeitig heruntergeladen werden. Bitte versuchen Sie es später erneut.", "May": "<PERSON>", "Memories accessible by LLMs will be shown here.": "Erinnerungen, die für Modelle zugänglich sind, werden hier angezeigt.", "Memory": "Erinnerungen", "Memory added successfully": "Erinnerung erfolgreich hinzugefügt", "Memory cleared successfully": "Erinnerung erfolgreich gelöscht", "Memory deleted successfully": "Erinnerung erfolgreich gelöscht", "Memory updated successfully": "Erinnerung erfolgreich aktualisiert", "Merge Responses": "Antworten zusammenführen", "Message rating should be enabled to use this feature": "Antwortbewertung muss aktiviert sein, um diese Funktion zu verwenden", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "<PERSON><PERSON><PERSON><PERSON>, die Si<PERSON> nach der Erstellung Ihres Links senden, werden nicht geteilt. Nutzer mit der URL können die freigegebene Unterhaltung einsehen.", "Min P": "<PERSON>", "Minimum Score": "Mindestpunktzahl", "Mirostat": "Mirostat", "Mirostat Eta": "Mirostat Eta", "Mirostat Tau": "Mirostat Tau", "MMMM DD, YYYY": "DD MMMM YYYY", "MMMM DD, YYYY HH:mm": "DD MMMM YYYY HH:mm", "MMMM DD, YYYY hh:mm:ss A": "DD MMMM YYYY HH:mm A", "Model": "<PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Modell '{{modelName}}' wurde erfolgreich heruntergeladen.", "Model '{{modelTag}}' is already in queue for downloading.": "Modell '{{modelTag}}' befindet sich bereits in der Warteschlange zum Herunterladen.", "Model {{modelId}} not found": "Modell {{modelId}} nicht gefunden", "Model {{modelName}} is not vision capable": "Das Modell {{modelName}} ist nicht für die Bildverarbeitung geeignet", "Model {{name}} is now {{status}}": "Modell {{name}} ist jetzt {{status}}", "Model accepts image inputs": "<PERSON><PERSON> ak<PERSON><PERSON><PERSON><PERSON>", "Model created successfully!": "Modell erfolgreich erstellt!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Modell-Dateisystempfad erkannt. Modellkurzname ist für das Update erforderlich, Fortsetzung nicht möglich.", "Model Filtering": "Modellfilterung", "Model ID": "Modell-ID", "Model IDs": "Modell-IDs", "Model Name": "Modell-Name", "Model not selected": "Modell nicht ausgewählt", "Model Params": "Modell-Parameter", "Model Permissions": "Modellberechtigungen", "Model updated successfully": "Modell erfolgreich aktualisiert", "Modelfile Content": "Modelfile-Inhalt", "Models": "<PERSON><PERSON>", "Models Access": "Modell-<PERSON><PERSON><PERSON>", "Models configuration saved successfully": "", "Mojeek Search API Key": "Mojeek Search API-Schlüssel", "more": "mehr", "More": "<PERSON><PERSON>", "Name": "Name", "Name your knowledge base": "Benennen Sie Ihren Wissensspeicher", "New Chat": "Neue Unterhaltung", "New folder": "", "New Password": "Neues Passwort", "new-channel": "", "No content found": "<PERSON><PERSON>halt gefunden", "No content to speak": "<PERSON>in Inhalt zum Vorlesen", "No distance available": "<PERSON><PERSON>", "No feedbacks found": "<PERSON><PERSON> gefunden", "No file selected": "<PERSON><PERSON> ausgewählt", "No files found.": "<PERSON><PERSON> gefunden.", "No groups with access, add a group to grant access": "<PERSON>ine Gruppen mit Zugriff, fügen Sie eine Gruppe hinzu, um Zugriff zu gewähren", "No HTML, CSS, or JavaScript content found.": "<PERSON><PERSON>-, CSS- oder JavaScript-Inhalte gefunden.", "No knowledge found": "<PERSON><PERSON>n gefunden", "No model IDs": "<PERSON><PERSON>-IDs", "No models found": "<PERSON><PERSON> gefunden", "No models selected": "", "No results found": "<PERSON><PERSON> gefunden", "No search query generated": "<PERSON><PERSON>", "No source available": "<PERSON><PERSON>", "No users were found.": "<PERSON><PERSON> gefunden.", "No valves to update": "<PERSON><PERSON> Val<PERSON> zum Aktualisieren", "None": "<PERSON><PERSON><PERSON>", "Not accurate": "", "Not relevant": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Hinweis: <PERSON><PERSON><PERSON> eine Mindestpunktzahl festlegen, werden in der Suche nur Dokumente mit einer Punktzahl größer oder gleich der Mindestpunktzahl zurückgegeben.", "Notes": "Notizen", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Benachrichtigungen", "November": "November", "num_gpu (Ollama)": "num_gpu (Ollama)", "num_thread (Ollama)": "num_thread (Ollama)", "OAuth ID": "OAuth-ID", "October": "Oktober", "Off": "Aus", "Okay, Let's Go!": "Okay, los geht's!", "OLED Dark": "OLED-Dunkel", "Ollama": "Ollama", "Ollama API": "Ollama-API", "Ollama API disabled": "Ollama-API deaktiviert", "Ollama API settings updated": "Ollama-API-Einstellungen aktualisiert", "Ollama Version": "Ollama-Version", "On": "Ein", "Only alphanumeric characters and hyphens are allowed": "Nur alphanumerische Zeichen und Bindestriche sind erlaubt", "Only alphanumeric characters and hyphens are allowed in the command string.": "In der Befehlszeichenfolge sind nur alphanumerische Zeichen und Bindestriche erlaubt.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Nur Sammlungen können bearbeitet werden. Erstellen Si<PERSON> eine neue Wissensbasis, um Dokumente zu bearbeiten/hinzuzufügen.", "Only select users and groups with permission can access": "Nur ausgewählte Benutzer und Gruppen mit Berechtigung können darauf zugreifen", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Hoppla! E<PERSON> scheint, dass die URL ungültig ist. Bitte überprüfen Sie diese und versuchen Si<PERSON> es erneut.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Hoppla! Es werden noch Dateien hochgeladen. <PERSON>te warten Si<PERSON>, bis der Upload abgeschlossen ist.", "Oops! There was an error in the previous response.": "Hoppla! Es gab einen <PERSON>hler in der vorherigen Antwort.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Hoppla! Sie verwenden eine nicht unterstützte Methode (nur Frontend). Bitte stellen Sie die WebUI vom Backend bereit.", "Open in full screen": "<PERSON><PERSON> Vollbild<PERSON>", "Open new chat": "Neuen Chat <PERSON>", "Open WebUI uses faster-whisper internally.": "Open WebUI verwendet intern faster-whisper.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Open WebUI verwendet SpeechT5 und CMU Arctic-Sprecher-Embeddings.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Die installierte Open-WebUI-Version (v{{OPEN_WEBUI_VERSION}}) ist niedriger als die erforderliche Version (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI-API", "OpenAI API Config": "OpenAI-API-Konfiguration", "OpenAI API Key is required.": "OpenAI-API-<PERSON><PERSON><PERSON><PERSON> erforderlich.", "OpenAI API settings updated": "OpenAI-API-Einstellungen aktualisiert", "OpenAI URL/Key required.": "OpenAI-URL/Schlüssel erford<PERSON>lich.", "or": "oder", "Organize your users": "Organisieren Sie Ihre <PERSON>er", "OUTPUT": "AUSGABE", "Output format": "Ausgabeformat", "Overview": "Übersicht", "page": "Seite", "Password": "Passwort", "Paste Large Text as File": "Großen Text als Datei einfügen", "PDF document (.pdf)": "PDF-Dokument (.pdf)", "PDF Extract Images (OCR)": "Text von Bildern aus PDFs extrahieren (OCR)", "pending": "ausstehend", "Permission denied when accessing media devices": "Zugriff auf Mediengeräte verweigert", "Permission denied when accessing microphone": "Zugriff auf das Mikrofon verweigert", "Permission denied when accessing microphone: {{error}}": "Zugriff auf das Mikrofon verweigert: {{error}}", "Permissions": "Berechtigungen", "Personalization": "Personalisierung", "Pin": "Anheften", "Pinned": "Angeheftet", "Pioneer insights": "Bahnbrechende Erkenntnisse", "Pipeline deleted successfully": "Pipeline erfolgreich <PERSON>", "Pipeline downloaded successfully": "Pipeline erfolgreich heruntergeladen", "Pipelines": "Pipelines", "Pipelines Not Detected": "Pipelines nicht erkannt", "Pipelines Valves": "Pipeline <PERSON>", "Plain text (.txt)": "Nur Text (.txt)", "Playground": "Testumgebung", "Please carefully review the following warnings:": "Bitte überprüfen Sie die folgenden Warnungen sorgfältig:", "Please enter a prompt": "<PERSON>te geben Sie einen Prompt ein", "Please fill in all fields.": "Bitte füllen Sie alle Felder aus.", "Please select a model first.": "", "Port": "Port", "Prefix ID": "Präfix-ID", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Prefix-ID wird verwendet, um Konflikte mit anderen Verbindungen zu vermeiden, indem ein Präfix zu den Modell-IDs hinzugefügt wird - leer lassen, um zu deaktivieren", "Previous 30 days": "Vorherige 30 Tage", "Previous 7 days": "Vorherige 7 Tage", "Profile Image": "Profilbild", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Prompt (z. B. \"<PERSON><PERSON><PERSON><PERSON>e mir eine interessante Tatsache über das Römische Reich\")", "Prompt Content": "Prompt-Inhalt", "Prompt created successfully": "Prompt erfolg<PERSON>ich erstellt", "Prompt suggestions": "Prompt-Vorschläge", "Prompt updated successfully": "Prompt erfolgreich aktualisiert", "Prompts": "Prompts", "Prompts Access": "Prompt-Zugriff", "Provide any specific details": "", "Proxy URL": "", "Pull \"{{searchValue}}\" from Ollama.com": "\"{{searchValue}}\" von Ollama.com beziehen", "Pull a model from Ollama.com": "Modell von Ollama.com beziehen", "Query Generation Prompt": "Abfragegenerierungsprompt", "Query Params": "Abfrageparameter", "RAG Template": "RAG-Vorlage", "Rating": "Bewertung", "Re-rank models by topic similarity": "Modelle nach thematischer Ähnlichkeit neu ordnen", "Read Aloud": "Vorlesen", "Record voice": "<PERSON><PERSON><PERSON> au<PERSON><PERSON><PERSON>", "Redirecting you to OpenWebUI Community": "Sie werden zur OpenWebUI-Community weitergeleitet", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative. (Default: 40)": "Reduziert die Wahrscheinlichkeit, <PERSON>sinn zu generieren. Ein höherer Wert (z.B. 100) liefert vielfältigere Antworten, während ein niedrigerer Wert (z.B. 10) konservativer ist. (Standard: 40)", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON><PERSON><PERSON> Sie sich auf sich selbst als \"Benutzer\" (z. B. \"Benutzer lernt Spanisch\")", "References from": "Referenzen aus", "Refresh Token Expiration": "", "Regenerate": "<PERSON><PERSON> gene<PERSON>", "Release Notes": "Veröffentlichungshinweise", "Relevance": "<PERSON><PERSON><PERSON><PERSON>", "Remove": "Entfernen", "Remove Model": "<PERSON><PERSON>", "Rename": "Umbenennen", "Reorder Models": "", "Repeat Last N": "Wiederhole die letzten N", "Reply in Thread": "", "Request Mode": "Anforderungsmodus", "Reranking Model": "Reranking-Modell", "Reranking model disabled": "Reranking-<PERSON><PERSON>", "Reranking model set to \"{{reranking_model}}\"": "Reranking-Modell \"{{reranking_model}}\" fesgelegt", "Reset": "Z<PERSON>ücksetzen", "Reset All Models": "", "Reset Upload Directory": "Upload-Verzeichnis zurücksetzen", "Reset Vector Storage/Knowledge": "Vektorspeicher/Wissen zurücksetzen", "Reset view": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Benachrichtigungen können nicht aktiviert werden, da die Website-Berechtigungen abgelehnt wurden. Bitte besuchen Sie Ihre Browser-Einstellungen, um den erforderlichen Zugriff zu gewähren.", "Response splitting": "Antwortaufteilung", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Rich-Text-Eingabe für Unterhaltungen", "RK": "RK", "Role": "<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Ausführen", "Running": "Läuft", "Save": "Speichern", "Save & Create": "<PERSON><PERSON><PERSON><PERSON>", "Save & Update": "Aktualisieren", "Save As Copy": "Als Ko<PERSON> s<PERSON>ichern", "Save Tag": "<PERSON> speichern", "Saved": "Gespe<PERSON>rt", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Das direkte Speichern von Unterhaltungen im Browser-Speicher wird nicht mehr unterstützt. Bitte nehmen Sie einen Moment Zeit, um Ihre Unterhaltungen zu exportieren und zu löschen, indem Sie auf die Schaltfläche unten klicken. <PERSON><PERSON>, Sie können Ihre Unterhaltungen problemlos über das Backend wieder importieren.", "Scroll to bottom when switching between branches": "<PERSON>im Wechsel zwischen Branches nach unten scrollen", "Search": "<PERSON><PERSON>", "Search a model": "<PERSON><PERSON> suchen", "Search Base": "<PERSON><PERSON><PERSON>", "Search Chats": "Unterhaltungen durchsuchen...", "Search Collection": "Sammlung durchsuchen", "Search Filters": "Suchfilter", "search for tags": "nach Tags suchen", "Search Functions": "Funktionen durchsuchen...", "Search Knowledge": "Wissen durchsuchen", "Search Models": "Modelle durchsuchen...", "Search options": "Suchoptionen", "Search Prompts": "Prompts durchsuchen...", "Search Result Count": "Anzahl der Suchergebnisse", "Search Tools": "Werkzeuge durchsuchen...", "Search users": "", "SearchApi API Key": "SearchApi-API-Schlüssel", "SearchApi Engine": "SearchApi-Engine", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "<PERSON><PERSON> nach \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "<PERSON>e im Wissen nach \"{{searchQuery}}\"", "Searxng Query URL": "Searxng-Abfrage-URL", "See readme.md for instructions": "Anleitung in readme.md anzeigen", "See what's new": "Entdecken Sie die Neuigkeiten", "Seed": "Seed", "Select a base model": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Basismodell", "Select a engine": "Wählen Sie eine Engine", "Select a function": "Wählen Sie eine Funktion", "Select a group": "Wählen Sie eine Gruppe", "Select a model": "<PERSON>ählen Sie ein Modell", "Select a pipeline": "Wählen Sie eine Pipeline", "Select a pipeline url": "Wählen Sie eine Pipeline-URL", "Select a tool": "Wählen Sie ein Werkzeug", "Select Engine": "Engine auswählen", "Select Knowledge": "Wissensdatenbank auswählen", "Select model": "<PERSON><PERSON> auswählen", "Select only one model to call": "<PERSON><PERSON><PERSON>en Si<PERSON> nur ein Modell zum Anrufen aus", "Selected model(s) do not support image inputs": "Ihre ausgewählten Modelle unterstützen keine Bildeingaben", "Semantic distance to query": "Semantische Distanz zur Abfrage", "Send": "Senden", "Send a message": "", "Send a Message": "Eine Nachricht senden", "Send message": "Nachricht senden", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Sendet `stream_options: { include_usage: true }` in der Anfrage.\nUnterstützte Anbieter geben Token-Nutzungsinformationen in der Antwort zurück, wenn dies festgelegt ist.", "September": "September", "Serper API Key": "Serper-API-Schlüssel", "Serply API Key": "Serply-API-Schlüssel", "Serpstack API Key": "Serpstack-API-Schlüssel", "Server connection verified": "Serververbindung überprüft", "Set as default": "Als Standard festlegen", "Set CFG Scale": "CFG-Skala festlegen", "Set Default Model": "Standardmodell festlegen", "Set embedding model": "Einbettungsmodell festlegen", "Set embedding model (e.g. {{model}})": "Einbettungsmodell festlegen (z. B. {{model}})", "Set Image Size": "Bildgröße festlegen", "Set reranking model (e.g. {{model}})": "Rerankingmodell festlegen (z. B. {{model}})", "Set Sampler": "Sampler festlegen", "Set Scheduler": "Scheduler festlegen", "Set Steps": "Schrittgröße festlegen", "Set Task Model": "Aufgabenmodell festlegen", "Set the number of GPU devices used for computation. This option controls how many GPU devices (if available) are used to process incoming requests. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "Legt die Anzahl der für die Berechnung verwendeten GPU-Geräte fest. Diese Option steuert, wie viele GPU-Geräte (falls verfügbar) zur Verarbeitung eingehender Anfragen verwendet werden. Eine Erhöhung dieses Wertes kann die Leistung für Modelle, die für GPU-Beschleunigung optimiert sind, erheblich verbessern, kann jedoch auch mehr Strom und GPU-Ressourcen verbrauchen.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Legt die Anzahl der für die Berechnung verwendeten GPU-Geräte fest. Diese Option steuert, wie viele GPU-Geräte (falls verfügbar) zur Verarbeitung eingehender Anfragen verwendet werden. Eine Erhöhung dieses Wertes kann die Leistung für Modelle, die für GPU-Beschleunigung optimiert sind, erheblich verbessern, kann jedoch auch mehr Strom und GPU-Ressourcen verbrauchen.", "Set Voice": "Stimme festlegen", "Set whisper model": "Whisper-<PERSON><PERSON> festlegen", "Sets how far back for the model to look back to prevent repetition. (Default: 64, 0 = disabled, -1 = num_ctx)": "Legt fest, wie weit das Modell zurückblicken soll, um Wiederholungen zu verhindern. (Standard: 64, 0 = deaktiviert, -1 = num_ctx)", "Sets how strongly to penalize repetitions. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. (Default: 1.1)": "<PERSON><PERSON> fest, wie stark Wiederholungen bestraft werden sollen. Ein höherer Wert (z.B. 1.5) bestraft Wiederholungen stärker, während ein niedrigerer Wert (z.B. 0.9) nachsichtiger ist. (Standard: 1.1)", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt. (Default: random)": "Legt den Zufallszahlengenerator-Seed für die Generierung fest. Wen<PERSON> dieser auf eine bestimmte Zahl gesetzt wird, erzeugt das Modell denselben Text für denselben Prompt. (Standard: zufällig)", "Sets the size of the context window used to generate the next token. (Default: 2048)": "Legt die Größe des Kontextfensters fest, das zur Generierung des nächsten Tokens verwendet wird. (Standard: 2048)", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Legt die zu verwendenden Stoppsequenzen fest. Wenn dieses Muster erkannt wird, stoppt das LLM die Textgenerierung und gibt zurück. Mehrere Stoppmuster können festgelegt werden, indem mehrere separate Stopp-Parameter in einer Modelldatei angegeben werden.", "Settings": "Einstellungen", "Settings saved successfully!": "Einstellungen erfolgreich gespeichert!", "Share": "Teilen", "Share Chat": "Unterhaltung teilen", "Share to OpenWebUI Community": "Mit OpenWebUI Community teilen", "Show": "Anzeigen", "Show \"What's New\" modal on login": "\"Was gib<PERSON>'s Neues\"-<PERSON><PERSON> beim Anmelden anzeigen", "Show Admin Details in Account Pending Overlay": "Admin-Details im Account-Pending-Overlay anzeigen", "Show shortcuts": "Verknüpfungen anzeigen", "Show your support!": "Zeigen Sie Ihre Unterstützung!", "Sign in": "Anmelden", "Sign in to {{WEBUI_NAME}}": "Bei {{WEBUI_NAME}} anmelden", "Sign in to {{WEBUI_NAME}} with LDAP": "Bei {{WEBUI_NAME}} mit LDAP anmelden", "Sign Out": "Abmelden", "Sign up": "Registrieren", "Sign up to {{WEBUI_NAME}}": "Bei {{WEBUI_NAME}} registrieren", "Signing in to {{WEBUI_NAME}}": "Wird bei {{WEBUI_NAME}} angemeldet", "sk-1234": "", "Source": "<PERSON><PERSON>", "Speech Playback Speed": "Sprachwiedergabegeschwindigkeit", "Speech recognition error: {{error}}": "Spracherkennungsfehler: {{error}}", "Speech-to-Text Engine": "<PERSON><PERSON><PERSON>-zu-Text-Engine", "Stop": "Stop", "Stop Sequence": "Stop-Sequenz", "Stream Chat Response": "Unterhaltungsantwort streamen", "STT Model": "STT-Modell", "STT Settings": "STT-Einstellungen", "Success": "Erfolg", "Successfully updated.": "Erfolgreich aktualisiert.", "Suggested prompts to get you started": "", "Support": "Unterstützung", "Support this plugin:": "Unterstützen Sie dieses Plugin:", "Sync directory": "Verzeichnis synchronisieren", "System": "System", "System Instructions": "Systemanweisungen", "System Prompt": "System-Prompt", "Tags Generation": "", "Tags Generation Prompt": "Prompt für Tag-Generierung", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting. (default: 1)": "Tail-Free Sampling wird verwen<PERSON>, um den Einfluss weniger wahrscheinlicher Tokens auf die Ausgabe zu reduzieren. Ein höherer Wert (z.B. 2.0) reduziert den Einfluss stärker, während ein Wert von 1.0 diese Einstellung deaktiviert. (Standard: 1)", "Tap to interrupt": "Zum Unterbrechen tippen", "Tavily API Key": "Tavily-API-Schlüssel", "Temperature": "Temperatur", "Template": "Vorlage", "Temporary Chat": "Temporäre Unterhaltung", "Text Splitter": "Text-Splitter", "Text-to-Speech Engine": "Text-zu-S<PERSON>che-Engine", "Tfs Z": "Tfs Z", "Thanks for your feedback!": "Danke für Ihr Feedback!", "The Application Account DN you bind with for search": "Der Anwendungs-Konto-DN, mit dem Sie für die Suche binden", "The base to search for users": "Die Basis, in der nach Benutzern gesucht wird", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.  (Default: 512)": "Die Batch-Größe bestimmt, wie viele Textanfragen gleichzeitig verarbeitet werden. Eine größere Batch-Größe kann die Leistung und Geschwindigkeit des Modells erhöhen, er<PERSON><PERSON> jedoch auch mehr Speicher. (Standard: 512)", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Die Entwickler hinter diesem Plugin sind leidenschaftliche Freiwillige aus der Community. Wenn Sie dieses Plugin hilfreich finden, erwägen <PERSON>, zu seiner Entwicklung beizutragen.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Die Bewertungs-Bestenliste basiert auf dem Elo-Bewertungssystem und wird in Echtzeit aktualisiert.", "The LDAP attribute that maps to the username that users use to sign in.": "Das LDAP-Attribut, das dem Benutzernamen zugeordnet ist, den Benutzer zum Anmelden verwenden.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Die Bestenliste befindet sich derzeit in der Beta-Phase, und es ist möglich, dass wir die Bewertungsberechnungen anpassen, während wir den Algorithmus verfeinern.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Die maximale Dateigröße in MB. Wenn die Dateigröße dieses Limit überschreitet, wird die Datei nicht hochgeladen.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Die maximale An<PERSON><PERSON> von <PERSON>, die gleichzeitig in der Unterhaltung verwendet werden können. Wenn die Anzahl der Dateien dieses Limit überschreitet, werden die Dateien nicht hochgeladen.", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Die Punktzahl sollte ein Wert zwischen 0,0 (0 %) und 1,0 (100 %) sein.", "The temperature of the model. Increasing the temperature will make the model answer more creatively. (Default: 0.8)": "Die Temperatur des Modells. Eine Erhöhung der Temperatur führt dazu, dass das Modell kreativer antwortet. (Standard: 0,8)", "Theme": "Design", "Thinking...": "Denke nach...", "This action cannot be undone. Do you wish to continue?": "Diese Aktion kann nicht rückgängig gemacht werden. Möchten Si<PERSON> fortfahren?", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Dies stellt sicher, dass Ihre wertvollen Unterhaltungen sicher in Ihrer Backend-Datenbank gespeichert werden. Vielen Dank!", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Dies ist eine experimentelle Funktion, sie funktioniert möglicherweise nicht wie erwartet und kann jederzeit geändert werden.", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics. (Default: 24)": "Diese Op<PERSON> steuert, wie viele Tokens beim Aktualisieren des Kontexts beibehalten werden. Wenn sie beispielsweise auf 2 gesetzt ist, werden die letzten 2 Tokens des Gesprächskontexts beibehalten. Das Beibehalten des Kontexts kann helfen, die Kontinuität eines Gesprächs aufrechtzuerhalten, kann jedoch die Fähigkeit verringern, auf neue Themen zu reagieren. (Standard: 24)", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.  (Default: 128)": "Diese Option legt die maximale An<PERSON>hl von To<PERSON>s fest, die das Modell in seiner Antwort generieren kann. Eine Erhöhung dieses Limits ermöglicht es dem Modell, längere Antworten zu geben, kann jedoch auch die Wahrscheinlichkeit erhöhen, dass unhilfreicher oder irrelevanter Inhalt generiert wird. (Standard: 128)", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Diese Option löscht alle vorhandenen Dateien in der Sammlung und ersetzt sie durch neu hochgeladene Dateien.", "This response was generated by \"{{model}}\"": "<PERSON><PERSON>rt wurde von \"{{model}}\" generiert", "This will delete": "<PERSON><PERSON>", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "<PERSON><PERSON> l<PERSON> <strong>{{NAME}}</strong> und <strong>alle Inhalte</strong>.", "This will delete all models including custom models": "Dies wird alle Modelle einschließlich benutzerdefinierter Modelle löschen", "This will delete all models including custom models and cannot be undone.": "Dies wird alle Modelle einschließlich benutzerdefinierter Modelle löschen und kann nicht rückgängig gemacht werden.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Dad<PERSON>ch wird die Wissensdatenbank zurückgesetzt und alle Dateien synchronisiert. Möchten Sie fortfahren?", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Tika-Server-U<PERSON>.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Tipp: Aktualisieren Sie mehrere Variablenfelder nacheinander, indem Si<PERSON> nach jedem Ersetzen die Tabulatortaste im Eingabefeld der Unterhaltung drücken.", "Title": "Titel", "Title (e.g. Tell me a fun fact)": "Titel (z. B. Erzähl mir einen lustigen Fakt)", "Title Auto-Generation": "Unterhaltungstitel automatisch generieren", "Title cannot be an empty string.": "<PERSON>itel darf nicht leer sein.", "Title Generation Prompt": "Prompt für Titelgenerierung", "TLS": "TLS", "To access the available model names for downloading,": "Um auf die verfügbaren Modellnamen zuzugreifen,", "To access the GGUF models available for downloading,": "Um auf die verfügbaren GGUF-Modelle zuzugreifen,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Um auf das WebUI zugreifen zu können, wenden <PERSON> sich bitte an einen Administrator. Administratoren können den Benutzerstatus über das Admin-Panel verwalten.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Um Wissensdatenbanken hier anzuh<PERSON>ngen, fügen Si<PERSON> sie zunächst dem Arbeitsbereich \"Wissen\" hinzu.", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Um Ihre Privatsphäre zu schützen, werden nur Bewertungen, Modell-IDs, Tags und Metadaten aus Ihrem Feedback geteilt – Ihre Unterhaltungen bleiben privat und werden nicht einbezogen.", "To select actions here, add them to the \"Functions\" workspace first.": "Um Aktionen auszuwählen, fügen Sie diese zunächst dem Arbeitsbereich „Funktionen“ hinzu.", "To select filters here, add them to the \"Functions\" workspace first.": "Um Filter auszuw<PERSON>hlen, fügen Si<PERSON> diese zunächst dem Arbeitsbereich „Funktionen“ hinzu.", "To select toolkits here, add them to the \"Tools\" workspace first.": "Um Toolkits auszuwählen, fügen Sie sie zunächst dem Arbeitsbereich „Werkzeuge“ hinzu.", "Toast notifications for new updates": "Toast-Benachrichtigungen für neue Updates", "Today": "<PERSON><PERSON>", "Toggle settings": "Einstellungen umschalten", "Toggle sidebar": "Seitenleiste umschalten", "Token": "Token", "Tokens To Keep On Context Refresh (num_keep)": "Beizubehaltende Tokens bei Kontextaktualisierung (num_keep)", "Tool created successfully": "Werkzeug erfolgreich erstellt", "Tool deleted successfully": "Werkzeug erfolgreich gelöscht", "Tool Description": "Werkzeugbeschreibung", "Tool ID": "Werkzeug-ID", "Tool imported successfully": "Werkzeug erfolgreich importiert", "Tool Name": "Werkzeugname", "Tool updated successfully": "Werkzeug erfolgreich aktualisiert", "Tools": "Werkzeuge", "Tools Access": "Werkzeugzugriff", "Tools are a function calling system with arbitrary code execution": "Wekzeuge sind ein Funktionssystem mit beliebiger Codeausführung", "Tools have a function calling system that allows arbitrary code execution": "Werkezuge verfügen über ein Funktionssystem, das die Ausführung beliebigen Codes ermöglicht", "Tools have a function calling system that allows arbitrary code execution.": "Werkzeuge verfügen über ein Funktionssystem, das die Ausführung beliebigen Codes ermöglicht.", "Top K": "Top K", "Top P": "Top P", "Transformers": "Transformers", "Trouble accessing Ollama?": "Probleme beim Zug<PERSON> auf Ollama?", "TTS Model": "TTS-Modell", "TTS Settings": "TTS-Einstellungen", "TTS Voice": "TTS-Stimme", "Type": "Art", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON><PERSON> Sie die Hugging Face Resolve-URL ein", "Uh-oh! There was an issue with the response.": "", "UI": "Oberfläche", "Unarchive All": "Alle wiederherstellen", "Unarchive All Archived Chats": "Alle archivierten Unterhaltungen wiederherstellen", "Unarchive Chat": "Unterhaltung wiederherstellen", "Unlock mysteries": "Geheimnisse entsperren", "Unpin": "<PERSON><PERSON><PERSON>", "Unravel secrets": "Geheimnisse lüften", "Untagged": "<PERSON><PERSON><PERSON><PERSON>", "Update": "Aktualisieren", "Update and Copy Link": "Aktualisieren und Link kopieren", "Update for the latest features and improvements.": "Aktualisieren Sie für die neuesten Funktionen und Verbesserungen.", "Update password": "Passwort aktualisieren", "Updated": "<PERSON>ktual<PERSON><PERSON>", "Updated at": "Aktualisiert am", "Updated At": "Aktualisiert am", "Upload": "Hochladen", "Upload a GGUF model": "GGUF-Model hochladen", "Upload directory": "Upload-Verzeichnis", "Upload files": "<PERSON><PERSON>", "Upload Files": "Datei(en) hochladen", "Upload Pipeline": "Pipeline hochladen", "Upload Progress": "Hochladefortschritt", "URL": "URL", "URL Mode": "URL-Modus", "USAi Chat can make mistakes. Review all responses for accuracy. Your agency’s AI and privacy policies apply.": "", "Use '#' in the prompt input to load and include your knowledge.": "Nutzen Sie '#' in der Prompt-Eingabe, um Ihr Wissen zu laden und einzuschließen.", "Use Gravatar": "Gravatar verwenden", "Use groups to group your users and assign permissions.": "Nutzen Sie Gruppen, um Ihre Benutzer zu gruppieren und Berechtigungen zuzuweisen.", "Use Initials": "<PERSON><PERSON> verwenden", "use_mlock (Ollama)": "use_mlock (Ollama)", "use_mmap (Ollama)": "use_mmap (Ollama)", "user": "<PERSON><PERSON><PERSON>", "User": "<PERSON><PERSON><PERSON>", "User location successfully retrieved.": "Benutzerstandort erfolgreich ermittelt.", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Users": "<PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "Verwendung des Standard-Arena-Modells mit allen Modellen. Klicken Sie auf die Plus-Schaltfläche, um benutzerdefinierte Modelle hinzuzufügen.", "Utilize": "Verwende", "Valid time units:": "Gültige Zeiteinheiten:", "Valves": "Valves", "Valves updated": "<PERSON><PERSON> aktual<PERSON>", "Valves updated successfully": "<PERSON>ves erfolgreich aktualisiert", "variable": "Variable", "variable to have them replaced with clipboard content.": "Variable, um den Inhalt der Zwischenablage beim Nutzen des Prompts zu ersetzen.", "Version": "Version", "Version {{selectedVersion}} of {{totalVersions}}": "Version {{selectedVersion}} von {{totalVersions}}", "Very bad": "", "View Replies": "", "Visibility": "Sichtbarkeit", "Voice": "Stimme", "Voice Input": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Warning": "<PERSON><PERSON><PERSON>", "Warning:": "Warnung:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Warnung: <PERSON><PERSON> dies aktivieren, können Benutzer beliebigen Code auf dem Server hochladen.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Warnung: <PERSON><PERSON> Si<PERSON> das Einbettungsmodell aktualisieren oder ändern, müssen Sie alle Dokumente erneut importieren.", "Web": "Web", "Web API": "Web-API", "Web Loader Settings": "Web Loader Einstellungen", "Web Search": "Websuche", "Web Search Engine": "Suchmaschine", "Web Search Query Generation": "", "Webhook URL": "Webhook URL", "WebUI Settings": "WebUI-Einstellungen", "WebUI URL": "", "WebUI will make requests to \"{{url}}/api/chat\"": "WebUI wird Anfragen an \"{{url}}/api/chat\" senden", "WebUI will make requests to \"{{url}}/chat/completions\"": "WebUI wird Anfragen an \"{{url}}/chat/completions\" senden", "Welcome, {{name}}!": "", "What are you trying to achieve?": "Was versuchen Si<PERSON> zu erreichen?", "What are you working on?": "Woran arbeiten Sie?", "What didn't you like about this response?": "", "What’s New in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "<PERSON><PERSON><PERSON><PERSON>, antwo<PERSON><PERSON> das Modell in Echtzeit auf jede Chat-Nachricht und generiert eine Antwort, sobald der Benutzer eine Nachricht sendet. Dieser Modus ist nützlich für Live-Chat-Anwendungen, kann jedoch die Leistung auf langsamerer Hardware beeinträchtigen.", "wherever you are": "wo immer Si<PERSON> sind", "Whisper (Local)": "Whisper (lokal)", "Widescreen Mode": "Breitbildmodus", "Won": "Gewonnen", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text. (Default: 0.9)": "Funktioniert zusammen mit top-k. Ein höherer Wert (z.B. 0,95) führt zu vielfältigerem Text, während ein niedrigerer Wert (z.B. 0,5) fokussierteren und konservativeren Text erzeugt. (Standard: 0,9)", "Workspace": "Arbeitsbereich", "Workspace Permissions": "Arbeitsbereichsberechtigungen", "Write a prompt suggestion (e.g. Who are you?)": "Schreiben Sie einen Promptvorschlag (z. B. Wer sind Sie?)", "Write a summary in 50 words that summarizes [topic or keyword].": "Schreibe eine kurze Zusammenfassung in 50 Wörtern, die [Thema oder Schlüsselwort] zusammenfasst.", "Write something...": "Schreiben Sie etwas...", "Write your model template content here": "Schreiben Sie hier Ihren Modellvorlageninhalt", "Yesterday": "Gestern", "You": "<PERSON><PERSON>", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "<PERSON>e können nur mit maximal {{maxCount}} Datei(en) gleichzeitig chatten.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Personalisieren Sie Interaktionen mit LLMs, indem Sie über die Schaltfläche \"Verwalten\" Erinnerungen hinzufügen.", "You cannot upload an empty file.": "<PERSON>e können keine leere Datei hochladen.", "You have no archived conversations.": "Du hast keine archivierten Unterhaltungen.", "You have shared this chat": "Sie haben diese Unterhaltung geteilt", "You're a helpful assistant.": "Du bist ein hilfreicher Assistent.", "Your account status is currently pending activation.": "Ihr Kontostatus ist derzeit ausstehend und wartet auf Aktivierung.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Ihr gesamter Beitrag geht direkt an den Plugin-Entwickler; Open WebUI behält keinen Prozentsatz ein. Die gewählte Finanzierungsplattform kann jedoch eigene Gebühren haben.", "Youtube": "YouTube", "Youtube Loader Settings": "YouTube-Ladeeinstellungen"}