var fo = Object.defineProperty;
var gn = (e) => {
	throw TypeError(e);
};
var vo = (e, t, r) =>
	t in e ? fo(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : (e[t] = r);
var te = (e, t, r) => vo(e, typeof t != 'symbol' ? t + '' : t, r),
	mn = (e, t, r) => t.has(e) || gn('Cannot ' + r);
var Q = (e, t, r) => (mn(e, t, 'read from private field'), r ? r.call(e) : t.get(e)),
	dr = (e, t, r) =>
		t.has(e)
			? gn('Cannot add the same private member more than once')
			: t instanceof WeakSet
				? t.add(e)
				: t.set(e, r),
	fr = (e, t, r, n) => (mn(e, t, 'write to private field'), n ? n.call(e, r) : t.set(e, r), r);
const ho = '5';
var An;
typeof window < 'u' &&
	((An = window.__svelte ?? (window.__svelte = {})).v ?? (An.v = /* @__PURE__ */ new Set())).add(
		ho
	);
const bo = 1,
	po = 2,
	go = 16,
	mo = 1,
	wo = 2,
	On = 4,
	_o = 8,
	ko = 16,
	qn = 1,
	yo = 2,
	Nr = '[',
	Ir = '[!',
	Tr = ']',
	at = {},
	ee = Symbol(),
	xo = 'http://www.w3.org/1999/xhtml',
	Eo = '@attach',
	wn = !1;
var Jt = Array.isArray,
	Co = Array.prototype.indexOf,
	zr = Array.from,
	Dt = Object.keys,
	kt = Object.defineProperty,
	Ue = Object.getOwnPropertyDescriptor,
	Pn = Object.getOwnPropertyDescriptors,
	So = Object.prototype,
	$o = Array.prototype,
	Mr = Object.getPrototypeOf,
	_n = Object.isExtensible;
function gt(e) {
	return typeof e == 'function';
}
const ve = () => {};
function No(e) {
	return e();
}
function yt(e) {
	for (var t = 0; t < e.length; t++) e[t]();
}
const he = 2,
	Lr = 4,
	It = 8,
	Ur = 16,
	De = 32,
	Qe = 64,
	Ar = 128,
	ne = 256,
	jt = 512,
	ie = 1024,
	qe = 2048,
	Je = 4096,
	Ae = 8192,
	Or = 16384,
	Dn = 32768,
	Tt = 65536,
	kn = 1 << 17,
	Io = 1 << 18,
	jn = 1 << 19,
	gr = 1 << 20,
	qr = 1 << 21,
	Te = Symbol('$state'),
	Pr = Symbol('legacy props'),
	To = Symbol(''),
	Hn = new (class extends Error {
		constructor() {
			super(...arguments);
			te(this, 'name', 'StaleReactionError');
			te(this, 'message', 'The reaction that called `getAbortSignal()` was re-run or destroyed');
		}
	})(),
	Vn = 3,
	xt = 8;
function Rn(e) {
	return e === this.v;
}
function Bn(e, t) {
	return e != e
		? t == t
		: e !== t || (e !== null && typeof e == 'object') || typeof e == 'function';
}
function Fn(e) {
	return !Bn(e, this.v);
}
function zo(e) {
	throw new Error('https://svelte.dev/e/effect_in_teardown');
}
function Mo() {
	throw new Error('https://svelte.dev/e/effect_in_unowned_derived');
}
function Lo(e) {
	throw new Error('https://svelte.dev/e/effect_orphan');
}
function Uo() {
	throw new Error('https://svelte.dev/e/effect_update_depth_exceeded');
}
function Ao() {
	throw new Error('https://svelte.dev/e/hydration_failed');
}
function Oo(e) {
	throw new Error('https://svelte.dev/e/props_invalid_value');
}
function qo() {
	throw new Error('https://svelte.dev/e/state_descriptors_fixed');
}
function Po() {
	throw new Error('https://svelte.dev/e/state_prototype_fixed');
}
function Do() {
	throw new Error('https://svelte.dev/e/state_unsafe_mutation');
}
let ut = !1,
	jo = !1;
function Ho() {
	ut = !0;
}
function Vo(e) {
	throw new Error('https://svelte.dev/e/lifecycle_outside_component');
}
let P = null;
function yn(e) {
	P = e;
}
function B(e, t = !1, r) {
	var n = (P = {
		p: P,
		c: null,
		d: !1,
		e: null,
		m: !1,
		s: e,
		x: null,
		l: null
	});
	ut &&
		!t &&
		(P.l = {
			s: null,
			u: null,
			r1: [],
			r2: st(!1)
		}),
		Xt(() => {
			n.d = !0;
		});
}
function F(e) {
	const t = P;
	if (t !== null) {
		e !== void 0 && (t.x = e);
		const s = t.e;
		if (s !== null) {
			var r = A,
				n = L;
			t.e = null;
			try {
				for (var i = 0; i < s.length; i++) {
					var o = s[i];
					Ze(o.effect), ze(o.reaction), ni(o.fn);
				}
			} finally {
				Ze(r), ze(n);
			}
		}
		(P = t.p), (t.m = !0);
	}
	return e || /** @type {T} */ {};
}
function Wt() {
	return !ut || (P !== null && P.l === null);
}
function ot(e) {
	if (typeof e != 'object' || e === null || Te in e) return e;
	const t = Mr(e);
	if (t !== So && t !== $o) return e;
	var r = /* @__PURE__ */ new Map(),
		n = Jt(e),
		i = /* @__PURE__ */ ge(0),
		o = L,
		s = (a) => {
			var l = L;
			ze(o);
			var u = a();
			return ze(l), u;
		};
	return (
		n &&
			r.set(
				'length',
				/* @__PURE__ */ ge(
					/** @type {any[]} */
					e.length
				)
			),
		new Proxy(
			/** @type {any} */
			e,
			{
				defineProperty(a, l, u) {
					(!('value' in u) || u.configurable === !1 || u.enumerable === !1 || u.writable === !1) &&
						qo();
					var d = r.get(l);
					return (
						d === void 0
							? (d = s(() => {
									var f = /* @__PURE__ */ ge(u.value);
									return r.set(l, f), f;
								}))
							: O(d, u.value, !0),
						!0
					);
				},
				deleteProperty(a, l) {
					var u = r.get(l);
					if (u === void 0) {
						if (l in a) {
							const c = s(() => /* @__PURE__ */ ge(ee));
							r.set(l, c), vr(i);
						}
					} else {
						if (n && typeof l == 'string') {
							var d =
									/** @type {Source<number>} */
									r.get('length'),
								f = Number(l);
							Number.isInteger(f) && f < d.v && O(d, f);
						}
						O(u, ee), vr(i);
					}
					return !0;
				},
				get(a, l, u) {
					var v;
					if (l === Te) return e;
					var d = r.get(l),
						f = l in a;
					if (
						(d === void 0 &&
							(!f || ((v = Ue(a, l)) != null && v.writable)) &&
							((d = s(() => {
								var b = ot(f ? a[l] : ee),
									g = /* @__PURE__ */ ge(b);
								return g;
							})),
							r.set(l, d)),
						d !== void 0)
					) {
						var c = E(d);
						return c === ee ? void 0 : c;
					}
					return Reflect.get(a, l, u);
				},
				getOwnPropertyDescriptor(a, l) {
					var u = Reflect.getOwnPropertyDescriptor(a, l);
					if (u && 'value' in u) {
						var d = r.get(l);
						d && (u.value = E(d));
					} else if (u === void 0) {
						var f = r.get(l),
							c = f == null ? void 0 : f.v;
						if (f !== void 0 && c !== ee)
							return {
								enumerable: !0,
								configurable: !0,
								value: c,
								writable: !0
							};
					}
					return u;
				},
				has(a, l) {
					var c;
					if (l === Te) return !0;
					var u = r.get(l),
						d = (u !== void 0 && u.v !== ee) || Reflect.has(a, l);
					if (u !== void 0 || (A !== null && (!d || ((c = Ue(a, l)) != null && c.writable)))) {
						u === void 0 &&
							((u = s(() => {
								var v = d ? ot(a[l]) : ee,
									b = /* @__PURE__ */ ge(v);
								return b;
							})),
							r.set(l, u));
						var f = E(u);
						if (f === ee) return !1;
					}
					return d;
				},
				set(a, l, u, d) {
					var p;
					var f = r.get(l),
						c = l in a;
					if (n && l === 'length')
						for (var v = u; v < /** @type {Source<number>} */ f.v; v += 1) {
							var b = r.get(v + '');
							b !== void 0
								? O(b, ee)
								: v in a && ((b = s(() => /* @__PURE__ */ ge(ee))), r.set(v + '', b));
						}
					if (f === void 0)
						(!c || ((p = Ue(a, l)) != null && p.writable)) &&
							((f = s(() => /* @__PURE__ */ ge(void 0))), O(f, ot(u)), r.set(l, f));
					else {
						c = f.v !== ee;
						var g = s(() => ot(u));
						O(f, g);
					}
					var _ = Reflect.getOwnPropertyDescriptor(a, l);
					if ((_ != null && _.set && _.set.call(d, u), !c)) {
						if (n && typeof l == 'string') {
							var m =
									/** @type {Source<number>} */
									r.get('length'),
								h = Number(l);
							Number.isInteger(h) && h >= m.v && O(m, h + 1);
						}
						vr(i);
					}
					return !0;
				},
				ownKeys(a) {
					E(i);
					var l = Reflect.ownKeys(a).filter((f) => {
						var c = r.get(f);
						return c === void 0 || c.v !== ee;
					});
					for (var [u, d] of r) d.v !== ee && !(u in a) && l.push(u);
					return l;
				},
				setPrototypeOf() {
					Po();
				}
			}
		)
	);
}
function vr(e, t = 1) {
	O(e, e.v + t);
}
function xn(e) {
	try {
		if (e !== null && typeof e == 'object' && Te in e) return e[Te];
	} catch {}
	return e;
}
function Ro(e, t) {
	return Object.is(xn(e), xn(t));
}
// @__NO_SIDE_EFFECTS__
function ct(e) {
	var t = he | qe,
		r = L !== null && (L.f & he) !== 0 ? /** @type {Derived} */ L : null;
	return (
		A === null || (r !== null && (r.f & ne) !== 0) ? (t |= ne) : (A.f |= jn),
		{
			ctx: P,
			deps: null,
			effects: null,
			equals: Rn,
			f: t,
			fn: e,
			reactions: null,
			rv: 0,
			v:
				/** @type {V} */
				null,
			wv: 0,
			parent: r ?? A,
			ac: null
		}
	);
}
// @__NO_SIDE_EFFECTS__
function J(e) {
	const t = /* @__PURE__ */ ct(e);
	return di(t), t;
}
// @__NO_SIDE_EFFECTS__
function Kt(e) {
	const t = /* @__PURE__ */ ct(e);
	return (t.equals = Fn), t;
}
function Yn(e) {
	var t = e.effects;
	if (t !== null) {
		e.effects = null;
		for (var r = 0; r < t.length; r += 1)
			oe(
				/** @type {Effect} */
				t[r]
			);
	}
}
function Bo(e) {
	for (var t = e.parent; t !== null; ) {
		if ((t.f & he) === 0)
			return (
				/** @type {Effect} */
				t
			);
		t = t.parent;
	}
	return null;
}
function Zn(e) {
	var t,
		r = A;
	Ze(Bo(e));
	try {
		Yn(e), (t = hi(e));
	} finally {
		Ze(r);
	}
	return t;
}
function Jn(e) {
	var t = Zn(e);
	if ((e.equals(t) || ((e.v = t), (e.wv = fi())), !vt)) {
		var r = (Fe || (e.f & ne) !== 0) && e.deps !== null ? Je : ie;
		ke(e, r);
	}
}
const Et = /* @__PURE__ */ new Map();
function st(e, t) {
	var r = {
		f: 0,
		// TODO ideally we could skip this altogether, but it causes type errors
		v: e,
		reactions: null,
		equals: Rn,
		rv: 0,
		wv: 0
	};
	return r;
}
// @__NO_SIDE_EFFECTS__
function ge(e, t) {
	const r = st(e);
	return di(r), r;
}
// @__NO_SIDE_EFFECTS__
function zt(e, t = !1, r = !0) {
	var i;
	const n = st(e);
	return (
		t || (n.equals = Fn),
		ut && r && P !== null && P.l !== null && ((i = P.l).s ?? (i.s = [])).push(n),
		n
	);
}
function O(e, t, r = !1) {
	L !== null && // since we are untracking the function inside `$inspect.with` we need to add this check
		// to ensure we error if state is set inside an inspect effect
		(!$e || (L.f & kn) !== 0) &&
		Wt() &&
		(L.f & (he | Ur | kn)) !== 0 &&
		!((V == null ? void 0 : V.reaction) === L && V.sources.includes(e)) &&
		Do();
	let n = r ? ot(t) : t;
	return Wn(e, n);
}
function Wn(e, t) {
	if (!e.equals(t)) {
		var r = e.v;
		vt ? Et.set(e, t) : Et.set(e, r),
			(e.v = t),
			(e.f & he) !== 0 &&
				((e.f & qe) !== 0 &&
					Zn(
						/** @type {Derived} */
						e
					),
				ke(e, (e.f & ne) === 0 ? ie : Je)),
			(e.wv = fi()),
			Kn(e, qe),
			Wt() &&
				A !== null &&
				(A.f & ie) !== 0 &&
				(A.f & (De | Qe)) === 0 &&
				(se === null ? na([e]) : se.push(e));
	}
	return t;
}
function En(e, t = 1) {
	var r = E(e),
		n = t === 1 ? r++ : r--;
	return O(e, r), n;
}
function Kn(e, t) {
	var r = e.reactions;
	if (r !== null)
		for (var n = Wt(), i = r.length, o = 0; o < i; o++) {
			var s = r[o],
				a = s.f;
			(a & qe) === 0 &&
				((!n && s === A) ||
					(ke(s, t),
					(a & (ie | ne)) !== 0 &&
						((a & he) !== 0
							? Kn(
									/** @type {Derived} */
									s,
									Je
								)
							: rr(
									/** @type {Effect} */
									s
								))));
		}
}
function Gt(e) {
	console.warn('https://svelte.dev/e/hydration_mismatch');
}
function Fo() {
	console.warn('https://svelte.dev/e/select_multiple_invalid_value');
}
let z = !1;
function we(e) {
	z = e;
}
let U;
function _e(e) {
	if (e === null) throw (Gt(), at);
	return (U = e);
}
function dt() {
	return _e(
		/** @type {TemplateNode} */
		/* @__PURE__ */ je(U)
	);
}
function C(e) {
	if (z) {
		if (/* @__PURE__ */ je(U) !== null) throw (Gt(), at);
		U = e;
	}
}
function Ne(e = 1) {
	if (z) {
		for (var t = e, r = U; t--; ) r = /** @type {TemplateNode} */ /* @__PURE__ */ je(r);
		U = r;
	}
}
function mr() {
	for (var e = 0, t = U; ; ) {
		if (t.nodeType === xt) {
			var r =
				/** @type {Comment} */
				t.data;
			if (r === Tr) {
				if (e === 0) return t;
				e -= 1;
			} else (r === Nr || r === Ir) && (e += 1);
		}
		var n =
			/** @type {TemplateNode} */
			/* @__PURE__ */ je(t);
		t.remove(), (t = n);
	}
}
function Gn(e) {
	if (!e || e.nodeType !== xt) throw (Gt(), at);
	return (
		/** @type {Comment} */
		e.data
	);
}
var Cn, Xn, Qn, ei;
function wr() {
	if (Cn === void 0) {
		(Cn = window), (Xn = /Firefox/.test(navigator.userAgent));
		var e = Element.prototype,
			t = Node.prototype,
			r = Text.prototype;
		(Qn = Ue(t, 'firstChild').get),
			(ei = Ue(t, 'nextSibling').get),
			_n(e) &&
				((e.__click = void 0),
				(e.__className = void 0),
				(e.__attributes = null),
				(e.__style = void 0),
				(e.__e = void 0)),
			_n(r) && (r.__t = void 0);
	}
}
function lt(e = '') {
	return document.createTextNode(e);
}
// @__NO_SIDE_EFFECTS__
function ce(e) {
	return Qn.call(e);
}
// @__NO_SIDE_EFFECTS__
function je(e) {
	return ei.call(e);
}
function S(e, t) {
	if (!z) return /* @__PURE__ */ ce(e);
	var r =
		/** @type {TemplateNode} */
		/* @__PURE__ */ ce(U);
	if (r === null) r = U.appendChild(lt());
	else if (t && r.nodeType !== Vn) {
		var n = lt();
		return r == null || r.before(n), _e(n), n;
	}
	return _e(r), r;
}
function Ye(e, t) {
	if (!z) {
		var r =
			/** @type {DocumentFragment} */
			/* @__PURE__ */ ce(
				/** @type {Node} */
				e
			);
		return r instanceof Comment && r.data === '' ? /* @__PURE__ */ je(r) : r;
	}
	return U;
}
function W(e, t = 1, r = !1) {
	let n = z ? U : e;
	for (var i; t--; ) (i = n), (n = /** @type {TemplateNode} */ /* @__PURE__ */ je(n));
	if (!z) return n;
	if (r && (n == null ? void 0 : n.nodeType) !== Vn) {
		var o = lt();
		return n === null ? i == null || i.after(o) : n.before(o), _e(o), o;
	}
	return _e(n), /** @type {TemplateNode} */ n;
}
function ti(e) {
	e.textContent = '';
}
function ri(e) {
	A === null && L === null && Lo(),
		L !== null && (L.f & ne) !== 0 && A === null && Mo(),
		vt && zo();
}
function Yo(e, t) {
	var r = t.last;
	r === null ? (t.last = t.first = e) : ((r.next = e), (e.prev = r), (t.last = e));
}
function He(e, t, r, n = !0) {
	var i = A,
		o = {
			ctx: P,
			deps: null,
			nodes_start: null,
			nodes_end: null,
			f: e | qe,
			first: null,
			fn: t,
			last: null,
			next: null,
			parent: i,
			b: i && i.b,
			prev: null,
			teardown: null,
			transitions: null,
			wv: 0,
			ac: null
		};
	if (r)
		try {
			tr(o), (o.f |= Dn);
		} catch (l) {
			throw (oe(o), l);
		}
	else t !== null && rr(o);
	var s =
		r &&
		o.deps === null &&
		o.first === null &&
		o.nodes_start === null &&
		o.teardown === null &&
		(o.f & (jn | Ar)) === 0;
	if (!s && n && (i !== null && Yo(o, i), L !== null && (L.f & he) !== 0)) {
		var a =
			/** @type {Derived} */
			L;
		(a.effects ?? (a.effects = [])).push(o);
	}
	return o;
}
function Xt(e) {
	const t = He(It, null, !1);
	return ke(t, ie), (t.teardown = e), t;
}
function _r(e) {
	ri();
	var t = A !== null && (A.f & De) !== 0 && P !== null && !P.m;
	if (t) {
		var r =
			/** @type {ComponentContext} */
			P;
		(r.e ?? (r.e = [])).push({
			fn: e,
			effect: A,
			reaction: L
		});
	} else return ni(e);
}
function ni(e) {
	return He(Lr | qr, e, !1);
}
function Zo(e) {
	return ri(), He(It | qr, e, !0);
}
function Jo(e) {
	const t = He(Qe, e, !0);
	return () => {
		oe(t);
	};
}
function Wo(e) {
	const t = He(Qe, e, !0);
	return (r = {}) =>
		new Promise((n) => {
			r.outro
				? Ct(t, () => {
						oe(t), n(void 0);
					})
				: (oe(t), n(void 0));
		});
}
function Dr(e) {
	return He(Lr, e, !1);
}
function Ko(e, t) {
	var r =
			/** @type {ComponentContextLegacy} */
			P,
		n = { effect: null, ran: !1 };
	r.l.r1.push(n),
		(n.effect = Qt(() => {
			e(), !n.ran && ((n.ran = !0), O(r.l.r2, !0), ht(t));
		}));
}
function Go() {
	var e =
		/** @type {ComponentContextLegacy} */
		P;
	Qt(() => {
		if (E(e.l.r2)) {
			for (var t of e.l.r1) {
				var r = t.effect;
				(r.f & ie) !== 0 && ke(r, Je), Mt(r) && tr(r), (t.ran = !1);
			}
			e.l.r2.v = !1;
		}
	});
}
function Qt(e) {
	return He(It, e, !0);
}
function ft(e, t = [], r = ct) {
	const n = t.map(r);
	return et(() => e(...n.map(E)));
}
function et(e, t = 0) {
	var r = He(It | Ur | t, e, !0);
	return r;
}
function Pe(e, t = !0) {
	return He(It | De, e, !0, t);
}
function ii(e) {
	var t = e.teardown;
	if (t !== null) {
		const r = vt,
			n = L;
		Sn(!0), ze(null);
		try {
			t.call(null);
		} finally {
			Sn(r), ze(n);
		}
	}
}
function oi(e, t = !1) {
	var i;
	var r = e.first;
	for (e.first = e.last = null; r !== null; ) {
		(i = r.ac) == null || i.abort(Hn);
		var n = r.next;
		(r.f & Qe) !== 0 ? (r.parent = null) : oe(r, t), (r = n);
	}
}
function Xo(e) {
	for (var t = e.first; t !== null; ) {
		var r = t.next;
		(t.f & De) === 0 && oe(t), (t = r);
	}
}
function oe(e, t = !0) {
	var r = !1;
	(t || (e.f & Io) !== 0) &&
		e.nodes_start !== null &&
		e.nodes_end !== null &&
		(Qo(
			e.nodes_start,
			/** @type {TemplateNode} */
			e.nodes_end
		),
		(r = !0)),
		oi(e, t && !r),
		Bt(e, 0),
		ke(e, Or);
	var n = e.transitions;
	if (n !== null) for (const o of n) o.stop();
	ii(e);
	var i = e.parent;
	i !== null && i.first !== null && ai(e),
		(e.next =
			e.prev =
			e.teardown =
			e.ctx =
			e.deps =
			e.fn =
			e.nodes_start =
			e.nodes_end =
			e.ac =
				null);
}
function Qo(e, t) {
	for (; e !== null; ) {
		var r = e === t ? null : /** @type {TemplateNode} */ /* @__PURE__ */ je(e);
		e.remove(), (e = r);
	}
}
function ai(e) {
	var t = e.parent,
		r = e.prev,
		n = e.next;
	r !== null && (r.next = n),
		n !== null && (n.prev = r),
		t !== null && (t.first === e && (t.first = n), t.last === e && (t.last = r));
}
function Ct(e, t) {
	var r = [];
	jr(e, r, !0),
		si(r, () => {
			oe(e), t && t();
		});
}
function si(e, t) {
	var r = e.length;
	if (r > 0) {
		var n = () => --r || t();
		for (var i of e) i.out(n);
	} else t();
}
function jr(e, t, r) {
	if ((e.f & Ae) === 0) {
		if (((e.f ^= Ae), e.transitions !== null))
			for (const s of e.transitions) (s.is_global || r) && t.push(s);
		for (var n = e.first; n !== null; ) {
			var i = n.next,
				o = (n.f & Tt) !== 0 || (n.f & De) !== 0;
			jr(n, t, o ? r : !1), (n = i);
		}
	}
}
function Ht(e) {
	li(e, !0);
}
function li(e, t) {
	if ((e.f & Ae) !== 0) {
		e.f ^= Ae;
		for (var r = e.first; r !== null; ) {
			var n = r.next,
				i = (r.f & Tt) !== 0 || (r.f & De) !== 0;
			li(r, i ? t : !1), (r = n);
		}
		if (e.transitions !== null) for (const o of e.transitions) (o.is_global || t) && o.in();
	}
}
let St = [],
	kr = [];
function ui() {
	var e = St;
	(St = []), yt(e);
}
function ea() {
	var e = kr;
	(kr = []), yt(e);
}
function er(e) {
	St.length === 0 && queueMicrotask(ui), St.push(e);
}
function ta() {
	St.length > 0 && ui(), kr.length > 0 && ea();
}
function ra(e) {
	var t =
		/** @type {Effect} */
		A;
	if ((t.f & Dn) === 0) {
		if ((t.f & Ar) === 0) throw e;
		t.fn(e);
	} else ci(e, t);
}
function ci(e, t) {
	for (; t !== null; ) {
		if ((t.f & Ar) !== 0)
			try {
				t.b.error(e);
				return;
			} catch {}
		t = t.parent;
	}
	throw e;
}
let $t = !1,
	Nt = null,
	Ge = !1,
	vt = !1;
function Sn(e) {
	vt = e;
}
let _t = [];
let L = null,
	$e = !1;
function ze(e) {
	L = e;
}
let A = null;
function Ze(e) {
	A = e;
}
let V = null;
function di(e) {
	L !== null && L.f & gr && (V === null ? (V = { reaction: L, sources: [e] }) : V.sources.push(e));
}
let X = null,
	re = 0,
	se = null;
function na(e) {
	se = e;
}
let Vt = 1,
	Rt = 0,
	Fe = !1;
function fi() {
	return ++Vt;
}
function Mt(e) {
	var f;
	var t = e.f;
	if ((t & qe) !== 0) return !0;
	if ((t & Je) !== 0) {
		var r = e.deps,
			n = (t & ne) !== 0;
		if (r !== null) {
			var i,
				o,
				s = (t & jt) !== 0,
				a = n && A !== null && !Fe,
				l = r.length;
			if (s || a) {
				var u =
						/** @type {Derived} */
						e,
					d = u.parent;
				for (i = 0; i < l; i++)
					(o = r[i]),
						(s || !((f = o == null ? void 0 : o.reactions) != null && f.includes(u))) &&
							(o.reactions ?? (o.reactions = [])).push(u);
				s && (u.f ^= jt), a && d !== null && (d.f & ne) === 0 && (u.f ^= ne);
			}
			for (i = 0; i < l; i++)
				if (
					((o = r[i]),
					Mt(
						/** @type {Derived} */
						o
					) &&
						Jn(
							/** @type {Derived} */
							o
						),
					o.wv > e.wv)
				)
					return !0;
		}
		(!n || (A !== null && !Fe)) && ke(e, ie);
	}
	return !1;
}
function vi(e, t, r = !0) {
	var n = e.reactions;
	if (n !== null)
		for (var i = 0; i < n.length; i++) {
			var o = n[i];
			((V == null ? void 0 : V.reaction) === L && V.sources.includes(e)) ||
				((o.f & he) !== 0
					? vi(
							/** @type {Derived} */
							o,
							t,
							!1
						)
					: t === o &&
						(r ? ke(o, qe) : (o.f & ie) !== 0 && ke(o, Je),
						rr(
							/** @type {Effect} */
							o
						)));
		}
}
function hi(e) {
	var v;
	var t = X,
		r = re,
		n = se,
		i = L,
		o = Fe,
		s = V,
		a = P,
		l = $e,
		u = e.f;
	(X = /** @type {null | Value[]} */ null),
		(re = 0),
		(se = null),
		(Fe = (u & ne) !== 0 && ($e || !Ge || L === null)),
		(L = (u & (De | Qe)) === 0 ? e : null),
		(V = null),
		yn(e.ctx),
		($e = !1),
		Rt++,
		(e.f |= gr),
		e.ac !== null && (e.ac.abort(Hn), (e.ac = null));
	try {
		var d =
				/** @type {Function} */
				(0, e.fn)(),
			f = e.deps;
		if (X !== null) {
			var c;
			if ((Bt(e, re), f !== null && re > 0))
				for (f.length = re + X.length, c = 0; c < X.length; c++) f[re + c] = X[c];
			else e.deps = f = X;
			if (
				!Fe || // Deriveds that already have reactions can cleanup, so we still add them as reactions
				((u & he) !== 0 && /** @type {import('#client').Derived} */ e.reactions !== null)
			)
				for (c = re; c < f.length; c++) ((v = f[c]).reactions ?? (v.reactions = [])).push(e);
		} else f !== null && re < f.length && (Bt(e, re), (f.length = re));
		if (Wt() && se !== null && !$e && f !== null && (e.f & (he | Je | qe)) === 0)
			for (c = 0; c < /** @type {Source[]} */ se.length; c++)
				vi(
					se[c],
					/** @type {Effect} */
					e
				);
		return (
			i !== null &&
				i !== e &&
				(Rt++, se !== null && (n === null ? (n = se) : n.push(.../** @type {Source[]} */ se))),
			d
		);
	} catch (b) {
		ra(b);
	} finally {
		(X = t), (re = r), (se = n), (L = i), (Fe = o), (V = s), yn(a), ($e = l), (e.f ^= gr);
	}
}
function ia(e, t) {
	let r = t.reactions;
	if (r !== null) {
		var n = Co.call(r, e);
		if (n !== -1) {
			var i = r.length - 1;
			i === 0 ? (r = t.reactions = null) : ((r[n] = r[i]), r.pop());
		}
	}
	r === null &&
		(t.f & he) !== 0 && // Destroying a child effect while updating a parent effect can cause a dependency to appear
		// to be unused, when in fact it is used by the currently-updating parent. Checking `new_deps`
		// allows us to skip the expensive work of disconnecting and immediately reconnecting it
		(X === null || !X.includes(t)) &&
		(ke(t, Je),
		(t.f & (ne | jt)) === 0 && (t.f ^= jt),
		Yn(
			/** @type {Derived} **/
			t
		),
		Bt(
			/** @type {Derived} **/
			t,
			0
		));
}
function Bt(e, t) {
	var r = e.deps;
	if (r !== null) for (var n = t; n < r.length; n++) ia(e, r[n]);
}
function tr(e) {
	var t = e.f;
	if ((t & Or) === 0) {
		ke(e, ie);
		var r = A,
			n = Ge;
		(A = e), (Ge = !0);
		try {
			(t & Ur) !== 0 ? Xo(e) : oi(e), ii(e);
			var i = hi(e);
			(e.teardown = typeof i == 'function' ? i : null), (e.wv = Vt);
			var o;
			wn && jo && (e.f & qe) !== 0 && e.deps;
		} finally {
			(Ge = n), (A = r);
		}
	}
}
function oa() {
	try {
		Uo();
	} catch (e) {
		if (Nt !== null) ci(e, Nt);
		else throw e;
	}
}
function bi() {
	var e = Ge;
	try {
		var t = 0;
		for (Ge = !0; _t.length > 0; ) {
			t++ > 1e3 && oa();
			var r = _t,
				n = r.length;
			_t = [];
			for (var i = 0; i < n; i++) {
				var o = sa(r[i]);
				aa(o);
			}
			Et.clear();
		}
	} finally {
		($t = !1), (Ge = e), (Nt = null);
	}
}
function aa(e) {
	var t = e.length;
	if (t !== 0) {
		for (var r = 0; r < t; r++) {
			var n = e[r];
			if ((n.f & (Or | Ae)) === 0 && Mt(n)) {
				var i = Vt;
				if (
					(tr(n),
					n.deps === null &&
						n.first === null &&
						n.nodes_start === null &&
						(n.teardown === null ? ai(n) : (n.fn = null)),
					Vt > i && (n.f & qr) !== 0)
				)
					break;
			}
		}
		for (; r < t; r += 1) rr(e[r]);
	}
}
function rr(e) {
	$t || (($t = !0), queueMicrotask(bi));
	for (var t = (Nt = e); t.parent !== null; ) {
		t = t.parent;
		var r = t.f;
		if ((r & (Qe | De)) !== 0) {
			if ((r & ie) === 0) return;
			t.f ^= ie;
		}
	}
	_t.push(t);
}
function sa(e) {
	for (var t = [], r = e; r !== null; ) {
		var n = r.f,
			i = (n & (De | Qe)) !== 0,
			o = i && (n & ie) !== 0;
		if (!o && (n & Ae) === 0) {
			(n & Lr) !== 0 ? t.push(r) : i ? (r.f ^= ie) : Mt(r) && tr(r);
			var s = r.first;
			if (s !== null) {
				r = s;
				continue;
			}
		}
		var a = r.parent;
		for (r = r.next; r === null && a !== null; ) (r = a.next), (a = a.parent);
	}
	return t;
}
function k(e) {
	for (var t; ; ) {
		if ((ta(), _t.length === 0)) return ($t = !1), (Nt = null), /** @type {T} */ t;
		($t = !0), bi();
	}
}
function E(e) {
	var t = e.f,
		r = (t & he) !== 0;
	if (L !== null && !$e) {
		if ((V == null ? void 0 : V.reaction) !== L || !(V != null && V.sources.includes(e))) {
			var n = L.deps;
			e.rv < Rt &&
				((e.rv = Rt),
				X === null && n !== null && n[re] === e
					? re++
					: X === null
						? (X = [e])
						: (!Fe || !X.includes(e)) && X.push(e));
		}
	} else if (
		r &&
		/** @type {Derived} */
		e.deps === null &&
		/** @type {Derived} */
		e.effects === null
	) {
		var i =
				/** @type {Derived} */
				e,
			o = i.parent;
		o !== null && (o.f & ne) === 0 && (i.f ^= ne);
	}
	return r && ((i = /** @type {Derived} */ e), Mt(i) && Jn(i)), vt && Et.has(e) ? Et.get(e) : e.v;
}
function ht(e) {
	var t = $e;
	try {
		return ($e = !0), e();
	} finally {
		$e = t;
	}
}
const la = -7169;
function ke(e, t) {
	e.f = (e.f & la) | t;
}
function pi(e) {
	if (!(typeof e != 'object' || !e || e instanceof EventTarget)) {
		if (Te in e) yr(e);
		else if (!Array.isArray(e))
			for (let t in e) {
				const r = e[t];
				typeof r == 'object' && r && Te in r && yr(r);
			}
	}
}
function yr(e, t = /* @__PURE__ */ new Set()) {
	if (
		typeof e == 'object' &&
		e !== null && // We don't want to traverse DOM elements
		!(e instanceof EventTarget) &&
		!t.has(e)
	) {
		t.add(e), e instanceof Date && e.getTime();
		for (let n in e)
			try {
				yr(e[n], t);
			} catch {}
		const r = Mr(e);
		if (
			r !== Object.prototype &&
			r !== Array.prototype &&
			r !== Map.prototype &&
			r !== Set.prototype &&
			r !== Date.prototype
		) {
			const n = Pn(r);
			for (let i in n) {
				const o = n[i].get;
				if (o)
					try {
						o.call(e);
					} catch {}
			}
		}
	}
}
function ua(e, t) {
	if (t) {
		const r = document.body;
		(e.autofocus = !0),
			er(() => {
				document.activeElement === r && e.focus();
			});
	}
}
function ca(e) {
	var t = L,
		r = A;
	ze(null), Ze(null);
	try {
		return e();
	} finally {
		ze(t), Ze(r);
	}
}
const gi = /* @__PURE__ */ new Set(),
	xr = /* @__PURE__ */ new Set();
function mi(e, t, r, n = {}) {
	function i(o) {
		if ((n.capture || wt.call(t, o), !o.cancelBubble))
			return ca(() => (r == null ? void 0 : r.call(this, o)));
	}
	return (
		e.startsWith('pointer') || e.startsWith('touch') || e === 'wheel'
			? er(() => {
					t.addEventListener(e, i, n);
				})
			: t.addEventListener(e, i, n),
		i
	);
}
function Z(e, t, r, n, i) {
	var o = { capture: n, passive: i },
		s = mi(e, t, r, o);
	(t === document.body || // @ts-ignore
		t === window || // @ts-ignore
		t === document || // Firefox has quirky behavior, it can happen that we still get "canplay" events when the element is already removed
		t instanceof HTMLMediaElement) &&
		Xt(() => {
			t.removeEventListener(e, s, o);
		});
}
function Hr(e) {
	for (var t = 0; t < e.length; t++) gi.add(e[t]);
	for (var r of xr) r(e);
}
function wt(e) {
	var h;
	var t = this,
		r =
			/** @type {Node} */
			t.ownerDocument,
		n = e.type,
		i = ((h = e.composedPath) == null ? void 0 : h.call(e)) || [],
		o =
			/** @type {null | Element} */
			i[0] || e.target,
		s = 0,
		a = e.__root;
	if (a) {
		var l = i.indexOf(a);
		if (l !== -1 && (t === document || t === /** @type {any} */ window)) {
			e.__root = t;
			return;
		}
		var u = i.indexOf(t);
		if (u === -1) return;
		l <= u && (s = l);
	}
	if (((o = /** @type {Element} */ i[s] || e.target), o !== t)) {
		kt(e, 'currentTarget', {
			configurable: !0,
			get() {
				return o || r;
			}
		});
		var d = L,
			f = A;
		ze(null), Ze(null);
		try {
			for (var c, v = []; o !== null; ) {
				var b = o.assignedSlot || o.parentNode || /** @type {any} */ o.host || null;
				try {
					var g = o['__' + n];
					if (
						g != null &&
						(!(/** @type {any} */ o.disabled) || // DOM could've been updated already by the time this is reached, so we check this as well
							// -> the target could not have been disabled because it emits the event in the first place
							e.target === o)
					)
						if (Jt(g)) {
							var [_, ...m] = g;
							_.apply(o, [e, ...m]);
						} else g.call(o, e);
				} catch (p) {
					c ? v.push(p) : (c = p);
				}
				if (e.cancelBubble || b === t || b === null) break;
				o = b;
			}
			if (c) {
				for (let p of v)
					queueMicrotask(() => {
						throw p;
					});
				throw c;
			}
		} finally {
			(e.__root = t), delete e.currentTarget, ze(d), Ze(f);
		}
	}
}
function wi(e) {
	var t = document.createElement('template');
	return (t.innerHTML = e.replaceAll('<!>', '<!---->')), t.content;
}
function Oe(e, t) {
	var r =
		/** @type {Effect} */
		A;
	r.nodes_start === null && ((r.nodes_start = e), (r.nodes_end = t));
}
// @__NO_SIDE_EFFECTS__
function be(e, t) {
	var r = (t & qn) !== 0,
		n = (t & yo) !== 0,
		i,
		o = !e.startsWith('<!>');
	return () => {
		if (z) return Oe(U, null), U;
		i === void 0 &&
			((i = wi(o ? e : '<!>' + e)), r || (i = /** @type {Node} */ /* @__PURE__ */ ce(i)));
		var s =
			/** @type {TemplateNode} */
			n || Xn ? document.importNode(i, !0) : i.cloneNode(!0);
		if (r) {
			var a =
					/** @type {TemplateNode} */
					/* @__PURE__ */ ce(s),
				l =
					/** @type {TemplateNode} */
					s.lastChild;
			Oe(a, l);
		} else Oe(s, s);
		return s;
	};
}
// @__NO_SIDE_EFFECTS__
function da(e, t, r = 'svg') {
	var n = !e.startsWith('<!>'),
		i = (t & qn) !== 0,
		o = `<${r}>${n ? e : '<!>' + e}</${r}>`,
		s;
	return () => {
		if (z) return Oe(U, null), U;
		if (!s) {
			var a =
					/** @type {DocumentFragment} */
					wi(o),
				l =
					/** @type {Element} */
					/* @__PURE__ */ ce(a);
			if (i)
				for (s = document.createDocumentFragment(); /* @__PURE__ */ ce(l); )
					s.appendChild(
						/** @type {Node} */
						/* @__PURE__ */ ce(l)
					);
			else s = /** @type {Element} */ /* @__PURE__ */ ce(l);
		}
		var u =
			/** @type {TemplateNode} */
			s.cloneNode(!0);
		if (i) {
			var d =
					/** @type {TemplateNode} */
					/* @__PURE__ */ ce(u),
				f =
					/** @type {TemplateNode} */
					u.lastChild;
			Oe(d, f);
		} else Oe(u, u);
		return u;
	};
}
// @__NO_SIDE_EFFECTS__
function j(e, t) {
	return /* @__PURE__ */ da(e, t, 'svg');
}
function Lt() {
	if (z) return Oe(U, null), U;
	var e = document.createDocumentFragment(),
		t = document.createComment(''),
		r = lt();
	return e.append(t, r), Oe(t, r), e;
}
function I(e, t) {
	if (z) {
		(A.nodes_end = U), dt();
		return;
	}
	e !== null &&
		e.before(
			/** @type {Node} */
			t
		);
}
function fa(e) {
	return e.endsWith('capture') && e !== 'gotpointercapture' && e !== 'lostpointercapture';
}
const va = [
	'beforeinput',
	'click',
	'change',
	'dblclick',
	'contextmenu',
	'focusin',
	'focusout',
	'input',
	'keydown',
	'keyup',
	'mousedown',
	'mousemove',
	'mouseout',
	'mouseover',
	'mouseup',
	'pointerdown',
	'pointermove',
	'pointerout',
	'pointerover',
	'pointerup',
	'touchend',
	'touchmove',
	'touchstart'
];
function ha(e) {
	return va.includes(e);
}
const ba = {
	// no `class: 'className'` because we handle that separately
	formnovalidate: 'formNoValidate',
	ismap: 'isMap',
	nomodule: 'noModule',
	playsinline: 'playsInline',
	readonly: 'readOnly',
	defaultvalue: 'defaultValue',
	defaultchecked: 'defaultChecked',
	srcobject: 'srcObject',
	novalidate: 'noValidate',
	allowfullscreen: 'allowFullscreen',
	disablepictureinpicture: 'disablePictureInPicture',
	disableremoteplayback: 'disableRemotePlayback'
};
function pa(e) {
	return (e = e.toLowerCase()), ba[e] ?? e;
}
const ga = ['touchstart', 'touchmove'];
function ma(e) {
	return ga.includes(e);
}
function Vr(e, t) {
	var r = t == null ? '' : typeof t == 'object' ? t + '' : t;
	r !== (e.__t ?? (e.__t = e.nodeValue)) && ((e.__t = r), (e.nodeValue = r + ''));
}
function _i(e, t) {
	return ki(e, t);
}
function wa(e, t) {
	wr(), (t.intro = t.intro ?? !1);
	const r = t.target,
		n = z,
		i = U;
	try {
		for (
			var o =
				/** @type {TemplateNode} */
				/* @__PURE__ */ ce(r);
			o && (o.nodeType !== xt || /** @type {Comment} */ o.data !== Nr);

		)
			o = /** @type {TemplateNode} */ /* @__PURE__ */ je(o);
		if (!o) throw at;
		we(!0),
			_e(
				/** @type {Comment} */
				o
			),
			dt();
		const s = ki(e, { ...t, anchor: o });
		if (U === null || U.nodeType !== xt || /** @type {Comment} */ U.data !== Tr) throw (Gt(), at);
		return we(!1), /**  @type {Exports} */ s;
	} catch (s) {
		if (s === at) return t.recover === !1 && Ao(), wr(), ti(r), we(!1), _i(e, t);
		throw s;
	} finally {
		we(n), _e(i);
	}
}
const rt = /* @__PURE__ */ new Map();
function ki(e, { target: t, anchor: r, props: n = {}, events: i, context: o, intro: s = !0 }) {
	wr();
	var a = /* @__PURE__ */ new Set(),
		l = (f) => {
			for (var c = 0; c < f.length; c++) {
				var v = f[c];
				if (!a.has(v)) {
					a.add(v);
					var b = ma(v);
					t.addEventListener(v, wt, { passive: b });
					var g = rt.get(v);
					g === void 0
						? (document.addEventListener(v, wt, { passive: b }), rt.set(v, 1))
						: rt.set(v, g + 1);
				}
			}
		};
	l(zr(gi)), xr.add(l);
	var u = void 0,
		d = Wo(() => {
			var f = r ?? t.appendChild(lt());
			return (
				Pe(() => {
					if (o) {
						B({});
						var c =
							/** @type {ComponentContext} */
							P;
						c.c = o;
					}
					i && (n.$$events = i),
						z &&
							Oe(
								/** @type {TemplateNode} */
								f,
								null
							),
						(u = e(f, n) || {}),
						z && (A.nodes_end = U),
						o && F();
				}),
				() => {
					var b;
					for (var c of a) {
						t.removeEventListener(c, wt);
						var v =
							/** @type {number} */
							rt.get(c);
						--v === 0 ? (document.removeEventListener(c, wt), rt.delete(c)) : rt.set(c, v);
					}
					xr.delete(l), f !== r && ((b = f.parentNode) == null || b.removeChild(f));
				}
			);
		});
	return Er.set(u, d), u;
}
let Er = /* @__PURE__ */ new WeakMap();
function _a(e, t) {
	const r = Er.get(e);
	return r ? (Er.delete(e), r(t)) : Promise.resolve();
}
function Cr(e, t, ...r) {
	var n = e,
		i = ve,
		o;
	et(() => {
		i !== (i = t()) &&
			(o && (oe(o), (o = null)),
			(o = Pe(() =>
				/** @type {SnippetFn} */
				i(n, ...r)
			)));
	}, Tt),
		z && (n = U);
}
function nr(e) {
	P === null && Vo(),
		ut && P.l !== null
			? ka(P).m.push(e)
			: _r(() => {
					const t = ht(e);
					if (typeof t == 'function')
						return (
							/** @type {() => void} */
							t
						);
				});
}
function ka(e) {
	var t =
		/** @type {ComponentContextLegacy} */
		e.l;
	return t.u ?? (t.u = { a: [], b: [], m: [] });
}
function de(e, t, [r, n] = [0, 0]) {
	z && r === 0 && dt();
	var i = e,
		o = null,
		s = null,
		a = ee,
		l = r > 0 ? Tt : 0,
		u = !1;
	const d = (c, v = !0) => {
			(u = !0), f(v, c);
		},
		f = (c, v) => {
			if (a === (a = c)) return;
			let b = !1;
			if (z && n !== -1) {
				if (r === 0) {
					const _ = Gn(i);
					_ === Nr
						? (n = 0)
						: _ === Ir
							? (n = 1 / 0)
							: ((n = parseInt(_.substring(1))), n !== n && (n = a ? 1 / 0 : -1));
				}
				const g = n > r;
				!!a === g && ((i = mr()), _e(i), we(!1), (b = !0), (n = -1));
			}
			a
				? (o ? Ht(o) : v && (o = Pe(() => v(i))),
					s &&
						Ct(s, () => {
							s = null;
						}))
				: (s ? Ht(s) : v && (s = Pe(() => v(i, [r + 1, n]))),
					o &&
						Ct(o, () => {
							o = null;
						})),
				b && we(!0);
		};
	et(() => {
		(u = !1), t(d), u || f(null, null);
	}, l),
		z && (i = U);
}
function ya(e, t) {
	return t;
}
function xa(e, t, r, n) {
	for (var i = [], o = t.length, s = 0; s < o; s++) jr(t[s].e, i, !0);
	var a = o > 0 && i.length === 0 && r !== null;
	if (a) {
		var l =
			/** @type {Element} */
			/** @type {Element} */
			r.parentNode;
		ti(l),
			l.append(
				/** @type {Element} */
				r
			),
			n.clear(),
			Be(e, t[0].prev, t[o - 1].next);
	}
	si(i, () => {
		for (var u = 0; u < o; u++) {
			var d = t[u];
			a || (n.delete(d.k), Be(e, d.prev, d.next)), oe(d.e, !a);
		}
	});
}
function Ea(e, t, r, n, i, o = null) {
	var s = e,
		a = { flags: t, items: /* @__PURE__ */ new Map(), first: null };
	{
		var l =
			/** @type {Element} */
			e;
		s = z
			? _e(
					/** @type {Comment | Text} */
					/* @__PURE__ */ ce(l)
				)
			: l.appendChild(lt());
	}
	z && dt();
	var u = null,
		d = !1,
		f = /* @__PURE__ */ Kt(() => {
			var c = r();
			return Jt(c) ? c : c == null ? [] : zr(c);
		});
	et(() => {
		var c = E(f),
			v = c.length;
		if (d && v === 0) return;
		d = v === 0;
		let b = !1;
		if (z) {
			var g = Gn(s) === Ir;
			g !== (v === 0) && ((s = mr()), _e(s), we(!1), (b = !0));
		}
		if (z) {
			for (var _ = null, m, h = 0; h < v; h++) {
				if (U.nodeType === xt && /** @type {Comment} */ U.data === Tr) {
					(s = /** @type {Comment} */ U), (b = !0), we(!1);
					break;
				}
				var p = c[h],
					w = n(p, h);
				(m = yi(U, a, _, null, p, w, h, i, t, r)), a.items.set(w, m), (_ = m);
			}
			v > 0 && _e(mr());
		}
		z || Ca(c, a, s, i, t, n, r),
			o !== null &&
				(v === 0
					? u
						? Ht(u)
						: (u = Pe(() => o(s)))
					: u !== null &&
						Ct(u, () => {
							u = null;
						})),
			b && we(!0),
			E(f);
	}),
		z && (s = U);
}
function Ca(e, t, r, n, i, o, s) {
	var a = e.length,
		l = t.items,
		u = t.first,
		d = u,
		f,
		c = null,
		v = [],
		b = [],
		g,
		_,
		m,
		h;
	for (h = 0; h < a; h += 1) {
		if (((g = e[h]), (_ = o(g, h)), (m = l.get(_)), m === void 0)) {
			var p = d ? /** @type {TemplateNode} */ d.e.nodes_start : r;
			(c = yi(p, t, c, c === null ? t.first : c.next, g, _, h, n, i, s)),
				l.set(_, c),
				(v = []),
				(b = []),
				(d = c.next);
			continue;
		}
		if ((Sa(m, g, h), (m.e.f & Ae) !== 0 && Ht(m.e), m !== d)) {
			if (f !== void 0 && f.has(m)) {
				if (v.length < b.length) {
					var w = b[0],
						x;
					c = w.prev;
					var M = v[0],
						$ = v[v.length - 1];
					for (x = 0; x < v.length; x += 1) $n(v[x], w, r);
					for (x = 0; x < b.length; x += 1) f.delete(b[x]);
					Be(t, M.prev, $.next),
						Be(t, c, M),
						Be(t, $, w),
						(d = w),
						(c = $),
						(h -= 1),
						(v = []),
						(b = []);
				} else
					f.delete(m),
						$n(m, d, r),
						Be(t, m.prev, m.next),
						Be(t, m, c === null ? t.first : c.next),
						Be(t, c, m),
						(c = m);
				continue;
			}
			for (v = [], b = []; d !== null && d.k !== _; )
				(d.e.f & Ae) === 0 && (f ?? (f = /* @__PURE__ */ new Set())).add(d),
					b.push(d),
					(d = d.next);
			if (d === null) continue;
			m = d;
		}
		v.push(m), (c = m), (d = m.next);
	}
	if (d !== null || f !== void 0) {
		for (var T = f === void 0 ? [] : zr(f); d !== null; )
			(d.e.f & Ae) === 0 && T.push(d), (d = d.next);
		var q = T.length;
		if (q > 0) {
			var H = a === 0 ? r : null;
			xa(t, T, H, l);
		}
	}
	(A.first = t.first && t.first.e), (A.last = c && c.e);
}
function Sa(e, t, r, n) {
	Wn(e.v, t), (e.i = r);
}
function yi(e, t, r, n, i, o, s, a, l, u) {
	var d = (l & bo) !== 0,
		f = (l & go) === 0,
		c = d ? (f ? /* @__PURE__ */ zt(i, !1, !1) : st(i)) : i,
		v = (l & po) === 0 ? s : st(s),
		b = {
			i: v,
			v: c,
			k: o,
			a: null,
			// @ts-expect-error
			e: null,
			prev: r,
			next: n
		};
	try {
		return (
			(b.e = Pe(() => a(e, c, v, u), z)),
			(b.e.prev = r && r.e),
			(b.e.next = n && n.e),
			r === null ? (t.first = b) : ((r.next = b), (r.e.next = b.e)),
			n !== null && ((n.prev = b), (n.e.prev = b.e)),
			b
		);
	} finally {
	}
}
function $n(e, t, r) {
	for (
		var n = e.next ? /** @type {TemplateNode} */ e.next.e.nodes_start : r,
			i = t ? /** @type {TemplateNode} */ t.e.nodes_start : r,
			o =
				/** @type {TemplateNode} */
				e.e.nodes_start;
		o !== n;

	) {
		var s =
			/** @type {TemplateNode} */
			/* @__PURE__ */ je(o);
		i.before(o), (o = s);
	}
}
function Be(e, t, r) {
	t === null ? (e.first = r) : ((t.next = r), (t.e.next = r && r.e)),
		r !== null && ((r.prev = t), (r.e.prev = t && t.e));
}
function ir(e, t, r, n, i) {
	var a;
	z && dt();
	var o = (a = t.$$slots) == null ? void 0 : a[r],
		s = !1;
	o === !0 && ((o = t.children), (s = !0)), o === void 0 || o(e, s ? () => n : n);
}
function $a(e, t, r) {
	z && dt();
	var n = e,
		i,
		o;
	et(() => {
		i !== (i = t()) && (o && (Ct(o), (o = null)), i && (o = Pe(() => r(n, i))));
	}, Tt),
		z && (n = U);
}
function bt(e, t) {
	er(() => {
		var r = e.getRootNode(),
			n =
				/** @type {ShadowRoot} */
				r.host
					? /** @type {ShadowRoot} */
						r
					: /** @type {Document} */
						(r.head ?? /** @type {Document} */ r.ownerDocument.head);
		if (!n.querySelector('#' + t.hash)) {
			const i = document.createElement('style');
			(i.id = t.hash), (i.textContent = t.code), n.appendChild(i);
		}
	});
}
function Na(e, t) {
	var r = void 0,
		n;
	et(() => {
		r !== (r = t()) &&
			(n && (oe(n), (n = null)),
			r &&
				(n = Pe(() => {
					Dr(() =>
						/** @type {(node: Element) => void} */
						r(e)
					);
				})));
	});
}
function xi(e) {
	var t,
		r,
		n = '';
	if (typeof e == 'string' || typeof e == 'number') n += e;
	else if (typeof e == 'object')
		if (Array.isArray(e)) {
			var i = e.length;
			for (t = 0; t < i; t++) e[t] && (r = xi(e[t])) && (n && (n += ' '), (n += r));
		} else for (r in e) e[r] && (n && (n += ' '), (n += r));
	return n;
}
function Ia() {
	for (var e, t, r = 0, n = '', i = arguments.length; r < i; r++)
		(e = arguments[r]) && (t = xi(e)) && (n && (n += ' '), (n += t));
	return n;
}
function Ta(e) {
	return typeof e == 'object' ? Ia(e) : (e ?? '');
}
const Nn = [
	...` 	
\r\f \v\uFEFF`
];
function za(e, t, r) {
	var n = e == null ? '' : '' + e;
	if ((t && (n = n ? n + ' ' + t : t), r)) {
		for (var i in r)
			if (r[i]) n = n ? n + ' ' + i : i;
			else if (n.length)
				for (var o = i.length, s = 0; (s = n.indexOf(i, s)) >= 0; ) {
					var a = s + o;
					(s === 0 || Nn.includes(n[s - 1])) && (a === n.length || Nn.includes(n[a]))
						? (n = (s === 0 ? '' : n.substring(0, s)) + n.substring(a + 1))
						: (s = a);
				}
	}
	return n === '' ? null : n;
}
function In(e, t = !1) {
	var r = t ? ' !important;' : ';',
		n = '';
	for (var i in e) {
		var o = e[i];
		o != null && o !== '' && (n += ' ' + i + ': ' + o + r);
	}
	return n;
}
function hr(e) {
	return e[0] !== '-' || e[1] !== '-' ? e.toLowerCase() : e;
}
function Ma(e, t) {
	if (t) {
		var r = '',
			n,
			i;
		if ((Array.isArray(t) ? ((n = t[0]), (i = t[1])) : (n = t), e)) {
			e = String(e)
				.replaceAll(/\s*\/\*.*?\*\/\s*/g, '')
				.trim();
			var o = !1,
				s = 0,
				a = !1,
				l = [];
			n && l.push(...Object.keys(n).map(hr)), i && l.push(...Object.keys(i).map(hr));
			var u = 0,
				d = -1;
			const g = e.length;
			for (var f = 0; f < g; f++) {
				var c = e[f];
				if (
					(a
						? c === '/' && e[f - 1] === '*' && (a = !1)
						: o
							? o === c && (o = !1)
							: c === '/' && e[f + 1] === '*'
								? (a = !0)
								: c === '"' || c === "'"
									? (o = c)
									: c === '('
										? s++
										: c === ')' && s--,
					!a && o === !1 && s === 0)
				) {
					if (c === ':' && d === -1) d = f;
					else if (c === ';' || f === g - 1) {
						if (d !== -1) {
							var v = hr(e.substring(u, d).trim());
							if (!l.includes(v)) {
								c !== ';' && f++;
								var b = e.substring(u, f).trim();
								r += ' ' + b + ';';
							}
						}
						(u = f + 1), (d = -1);
					}
				}
			}
		}
		return n && (r += In(n)), i && (r += In(i, !0)), (r = r.trim()), r === '' ? null : r;
	}
	return e == null ? null : String(e);
}
function ue(e, t, r, n, i, o) {
	var s = e.__className;
	if (z || s !== r || s === void 0) {
		var a = za(r, n, o);
		(!z || a !== e.getAttribute('class')) &&
			(a == null ? e.removeAttribute('class') : t ? (e.className = a) : e.setAttribute('class', a)),
			(e.__className = r);
	} else if (o && i !== o)
		for (var l in o) {
			var u = !!o[l];
			(i == null || u !== !!i[l]) && e.classList.toggle(l, u);
		}
	return o;
}
function br(e, t = {}, r, n) {
	for (var i in r) {
		var o = r[i];
		t[i] !== o && (r[i] == null ? e.style.removeProperty(i) : e.style.setProperty(i, o, n));
	}
}
function La(e, t, r, n) {
	var i = e.__style;
	if (z || i !== t) {
		var o = Ma(t, n);
		(!z || o !== e.getAttribute('style')) &&
			(o == null ? e.removeAttribute('style') : (e.style.cssText = o)),
			(e.__style = t);
	} else
		n &&
			(Array.isArray(n)
				? (br(e, r == null ? void 0 : r[0], n[0]),
					br(e, r == null ? void 0 : r[1], n[1], 'important'))
				: br(e, r, n));
	return n;
}
function Sr(e, t, r = !1) {
	if (e.multiple) {
		if (t == null) return;
		if (!Jt(t)) return Fo();
		for (var n of e.options) n.selected = t.includes(Tn(n));
		return;
	}
	for (n of e.options) {
		var i = Tn(n);
		if (Ro(i, t)) {
			n.selected = !0;
			return;
		}
	}
	(!r || t !== void 0) && (e.selectedIndex = -1);
}
function Ua(e) {
	var t = new MutationObserver(() => {
		Sr(e, e.__value);
	});
	t.observe(e, {
		// Listen to option element changes
		childList: !0,
		subtree: !0,
		// because of <optgroup>
		// Listen to option element value attribute changes
		// (doesn't get notified of select value changes,
		// because that property is not reflected as an attribute)
		attributes: !0,
		attributeFilter: ['value']
	}),
		Xt(() => {
			t.disconnect();
		});
}
function Tn(e) {
	return '__value' in e ? e.__value : e.value;
}
const it = Symbol('class'),
	mt = Symbol('style'),
	Ei = Symbol('is custom element'),
	Ci = Symbol('is html');
function Aa(e, t) {
	t ? e.hasAttribute('selected') || e.setAttribute('selected', '') : e.removeAttribute('selected');
}
function G(e, t, r, n) {
	var i = Si(e);
	(z &&
		((i[t] = e.getAttribute(t)),
		t === 'src' || t === 'srcset' || (t === 'href' && e.nodeName === 'LINK'))) ||
		(i[t] !== (i[t] = r) &&
			(t === 'loading' && (e[To] = r),
			r == null
				? e.removeAttribute(t)
				: typeof r != 'string' && $i(e).includes(t)
					? (e[t] = r)
					: e.setAttribute(t, r)));
}
function Oa(e, t, r, n, i = !1) {
	var o = Si(e),
		s = o[Ei],
		a = !o[Ci];
	let l = z && s;
	l && we(!1);
	var u = t || {},
		d = e.tagName === 'OPTION';
	for (var f in t) f in r || (r[f] = null);
	r.class ? (r.class = Ta(r.class)) : (n || r[it]) && (r.class = null),
		r[mt] && (r.style ?? (r.style = null));
	var c = $i(e);
	for (const p in r) {
		let w = r[p];
		if (d && p === 'value' && w == null) {
			(e.value = e.__value = ''), (u[p] = w);
			continue;
		}
		if (p === 'class') {
			var v = e.namespaceURI === 'http://www.w3.org/1999/xhtml';
			ue(e, v, w, n, t == null ? void 0 : t[it], r[it]), (u[p] = w), (u[it] = r[it]);
			continue;
		}
		if (p === 'style') {
			La(e, w, t == null ? void 0 : t[mt], r[mt]), (u[p] = w), (u[mt] = r[mt]);
			continue;
		}
		var b = u[p];
		if (!(w === b && !(w === void 0 && e.hasAttribute(p)))) {
			u[p] = w;
			var g = p[0] + p[1];
			if (g !== '$$')
				if (g === 'on') {
					const x = {},
						M = '$$' + p;
					let $ = p.slice(2);
					var _ = ha($);
					if ((fa($) && (($ = $.slice(0, -7)), (x.capture = !0)), !_ && b)) {
						if (w != null) continue;
						e.removeEventListener($, u[M], x), (u[M] = null);
					}
					if (w != null)
						if (_) (e[`__${$}`] = w), Hr([$]);
						else {
							let T = function (q) {
								u[p].call(this, q);
							};
							u[M] = mi($, e, T, x);
						}
					else _ && (e[`__${$}`] = void 0);
				} else if (p === 'style') G(e, p, w);
				else if (p === 'autofocus')
					ua(
						/** @type {HTMLElement} */
						e,
						!!w
					);
				else if (!s && (p === '__value' || (p === 'value' && w != null))) e.value = e.__value = w;
				else if (p === 'selected' && d)
					Aa(
						/** @type {HTMLOptionElement} */
						e,
						w
					);
				else {
					var m = p;
					a || (m = pa(m));
					var h = m === 'defaultValue' || m === 'defaultChecked';
					if (w == null && !s && !h)
						if (((o[p] = null), m === 'value' || m === 'checked')) {
							let x =
								/** @type {HTMLInputElement} */
								e;
							const M = t === void 0;
							if (m === 'value') {
								let $ = x.defaultValue;
								x.removeAttribute(m), (x.defaultValue = $), (x.value = x.__value = M ? $ : null);
							} else {
								let $ = x.defaultChecked;
								x.removeAttribute(m), (x.defaultChecked = $), (x.checked = M ? $ : !1);
							}
						} else e.removeAttribute(p);
					else
						h || (c.includes(m) && (s || typeof w != 'string'))
							? (e[m] = w)
							: typeof w != 'function' && G(e, m, w);
				}
		}
	}
	return l && we(!0), u;
}
function Ft(e, t, r = [], n, i = !1, o = ct) {
	const s = r.map(o);
	var a = void 0,
		l = {},
		u = e.nodeName === 'SELECT',
		d = !1;
	if (
		(et(() => {
			var c = t(...s.map(E)),
				v = Oa(e, a, c, n, i);
			d &&
				u &&
				'value' in c &&
				Sr(
					/** @type {HTMLSelectElement} */
					e,
					c.value
				);
			for (let g of Object.getOwnPropertySymbols(l)) c[g] || oe(l[g]);
			for (let g of Object.getOwnPropertySymbols(c)) {
				var b = c[g];
				g.description === Eo &&
					(!a || b !== a[g]) &&
					(l[g] && oe(l[g]), (l[g] = Pe(() => Na(e, () => b)))),
					(v[g] = b);
			}
			a = v;
		}),
		u)
	) {
		var f =
			/** @type {HTMLSelectElement} */
			e;
		Dr(() => {
			Sr(
				f,
				/** @type {Record<string | symbol, any>} */
				a.value,
				!0
			),
				Ua(f);
		});
	}
	d = !0;
}
function Si(e) {
	return (
		/** @type {Record<string | symbol, unknown>} **/
		// @ts-expect-error
		e.__attributes ??
		(e.__attributes = {
			[Ei]: e.nodeName.includes('-'),
			[Ci]: e.namespaceURI === xo
		})
	);
}
var zn = /* @__PURE__ */ new Map();
function $i(e) {
	var t = zn.get(e.nodeName);
	if (t) return t;
	zn.set(e.nodeName, (t = []));
	for (var r, n = e, i = Element.prototype; i !== n; ) {
		r = Pn(n);
		for (var o in r) r[o].set && t.push(o);
		n = Mr(n);
	}
	return t;
}
function Mn(e, t) {
	return e === t || (e == null ? void 0 : e[Te]) === t;
}
function Ni(e = {}, t, r, n) {
	return (
		Dr(() => {
			var i, o;
			return (
				Qt(() => {
					(i = o),
						(o = []),
						ht(() => {
							e !== r(...o) && (t(e, ...o), i && Mn(r(...i), e) && t(null, ...i));
						});
				}),
				() => {
					er(() => {
						o && Mn(r(...o), e) && t(null, ...o);
					});
				}
			);
		}),
		e
	);
}
function Ii(e = !1) {
	const t =
			/** @type {ComponentContextLegacy} */
			P,
		r = t.l.u;
	if (!r) return;
	let n = () => pi(t.s);
	if (e) {
		let i = 0,
			o =
				/** @type {Record<string, any>} */
				{};
		const s = /* @__PURE__ */ ct(() => {
			let a = !1;
			const l = t.s;
			for (const u in l) l[u] !== o[u] && ((o[u] = l[u]), (a = !0));
			return a && i++, i;
		});
		n = () => E(s);
	}
	r.b.length &&
		Zo(() => {
			Ln(t, n), yt(r.b);
		}),
		_r(() => {
			const i = ht(() => r.m.map(No));
			return () => {
				for (const o of i) typeof o == 'function' && o();
			};
		}),
		r.a.length &&
			_r(() => {
				Ln(t, n), yt(r.a);
			});
}
function Ln(e, t) {
	if (e.l.s) for (const r of e.l.s) E(r);
	t();
}
function Rr(e, t, r) {
	if (e == null) return t(void 0), r && r(void 0), ve;
	const n = ht(() =>
		e.subscribe(
			t,
			// @ts-expect-error
			r
		)
	);
	return n.unsubscribe ? () => n.unsubscribe() : n;
}
const nt = [];
function qa(e, t) {
	return {
		subscribe: or(e, t).subscribe
	};
}
function or(e, t = ve) {
	let r = null;
	const n = /* @__PURE__ */ new Set();
	function i(a) {
		if (Bn(e, a) && ((e = a), r)) {
			const l = !nt.length;
			for (const u of n) u[1](), nt.push(u, e);
			if (l) {
				for (let u = 0; u < nt.length; u += 2) nt[u][0](nt[u + 1]);
				nt.length = 0;
			}
		}
	}
	function o(a) {
		i(
			a(
				/** @type {T} */
				e
			)
		);
	}
	function s(a, l = ve) {
		const u = [a, l];
		return (
			n.add(u),
			n.size === 1 && (r = t(i, o) || ve),
			a(
				/** @type {T} */
				e
			),
			() => {
				n.delete(u), n.size === 0 && r && (r(), (r = null));
			}
		);
	}
	return { set: i, update: o, subscribe: s };
}
function Pa(e, t, r) {
	const n = !Array.isArray(e),
		i = n ? [e] : e;
	if (!i.every(Boolean)) throw new Error('derived() expects stores as input, got a falsy value');
	const o = t.length < 2;
	return qa(r, (s, a) => {
		let l = !1;
		const u = [];
		let d = 0,
			f = ve;
		const c = () => {
				if (d) return;
				f();
				const b = t(n ? u[0] : u, s, a);
				o ? s(b) : (f = typeof b == 'function' ? b : ve);
			},
			v = i.map((b, g) =>
				Rr(
					b,
					(_) => {
						(u[g] = _), (d &= ~(1 << g)), l && c();
					},
					() => {
						d |= 1 << g;
					}
				)
			);
		return (
			(l = !0),
			c(),
			function () {
				yt(v), f(), (l = !1);
			}
		);
	});
}
function Da(e) {
	let t;
	return Rr(e, (r) => (t = r))(), t;
}
let Ot = !1,
	$r = Symbol();
function fe(e, t, r) {
	const n =
		r[t] ??
		(r[t] = {
			store: null,
			source: /* @__PURE__ */ zt(void 0),
			unsubscribe: ve
		});
	if (n.store !== e && !($r in r))
		if ((n.unsubscribe(), (n.store = e ?? null), e == null))
			(n.source.v = void 0), (n.unsubscribe = ve);
		else {
			var i = !0;
			(n.unsubscribe = Rr(e, (o) => {
				i ? (n.source.v = o) : O(n.source, o);
			})),
				(i = !1);
		}
	return e && $r in r ? Da(e) : E(n.source);
}
function Ut() {
	const e = {};
	function t() {
		Xt(() => {
			for (var r in e) e[r].unsubscribe();
			kt(e, $r, {
				enumerable: !1,
				value: !0
			});
		});
	}
	return [e, t];
}
function ja(e) {
	var t = Ot;
	try {
		return (Ot = !1), [e(), Ot];
	} finally {
		Ot = t;
	}
}
const Ha = {
	get(e, t) {
		if (!e.exclude.includes(t)) return e.props[t];
	},
	set(e, t) {
		return !1;
	},
	getOwnPropertyDescriptor(e, t) {
		if (!e.exclude.includes(t) && t in e.props)
			return {
				enumerable: !0,
				configurable: !0,
				value: e.props[t]
			};
	},
	has(e, t) {
		return e.exclude.includes(t) ? !1 : t in e.props;
	},
	ownKeys(e) {
		return Reflect.ownKeys(e.props).filter((t) => !e.exclude.includes(t));
	}
};
// @__NO_SIDE_EFFECTS__
function Br(e, t, r) {
	return new Proxy({ props: e, exclude: t }, Ha);
}
const Va = {
	get(e, t) {
		if (!e.exclude.includes(t)) return E(e.version), t in e.special ? e.special[t]() : e.props[t];
	},
	set(e, t, r) {
		return (
			t in e.special ||
				(e.special[t] = y(
					{
						get [t]() {
							return e.props[t];
						}
					},
					/** @type {string} */
					t,
					On
				)),
			e.special[t](r),
			En(e.version),
			!0
		);
	},
	getOwnPropertyDescriptor(e, t) {
		if (!e.exclude.includes(t) && t in e.props)
			return {
				enumerable: !0,
				configurable: !0,
				value: e.props[t]
			};
	},
	deleteProperty(e, t) {
		return e.exclude.includes(t) || (e.exclude.push(t), En(e.version)), !0;
	},
	has(e, t) {
		return e.exclude.includes(t) ? !1 : t in e.props;
	},
	ownKeys(e) {
		return Reflect.ownKeys(e.props).filter((t) => !e.exclude.includes(t));
	}
};
function Yt(e, t) {
	return new Proxy({ props: e, exclude: t, special: {}, version: st(0) }, Va);
}
const Ra = {
	get(e, t) {
		let r = e.props.length;
		for (; r--; ) {
			let n = e.props[r];
			if ((gt(n) && (n = n()), typeof n == 'object' && n !== null && t in n)) return n[t];
		}
	},
	set(e, t, r) {
		let n = e.props.length;
		for (; n--; ) {
			let i = e.props[n];
			gt(i) && (i = i());
			const o = Ue(i, t);
			if (o && o.set) return o.set(r), !0;
		}
		return !1;
	},
	getOwnPropertyDescriptor(e, t) {
		let r = e.props.length;
		for (; r--; ) {
			let n = e.props[r];
			if ((gt(n) && (n = n()), typeof n == 'object' && n !== null && t in n)) {
				const i = Ue(n, t);
				return i && !i.configurable && (i.configurable = !0), i;
			}
		}
	},
	has(e, t) {
		if (t === Te || t === Pr) return !1;
		for (let r of e.props) if ((gt(r) && (r = r()), r != null && t in r)) return !0;
		return !1;
	},
	ownKeys(e) {
		const t = [];
		for (let r of e.props)
			if ((gt(r) && (r = r()), !!r)) {
				for (const n in r) t.includes(n) || t.push(n);
				for (const n of Object.getOwnPropertySymbols(r)) t.includes(n) || t.push(n);
			}
		return t;
	}
};
function Ti(...e) {
	return new Proxy({ props: e }, Ra);
}
function Ba(e) {
	var t;
	return ((t = e.ctx) == null ? void 0 : t.d) ?? !1;
}
function y(e, t, r, n) {
	var m;
	var i = !ut || (r & wo) !== 0,
		o = (r & _o) !== 0,
		s = (r & ko) !== 0,
		a =
			/** @type {V} */
			n,
		l = !0,
		u = () => (
			l &&
				((l = !1),
				(a = s
					? ht(
							/** @type {() => V} */
							n
						)
					: /** @type {V} */
						n)),
			a
		),
		d;
	if (o) {
		var f = Te in e || Pr in e;
		d = ((m = Ue(e, t)) == null ? void 0 : m.set) ?? (f && t in e ? (h) => (e[t] = h) : void 0);
	}
	var c,
		v = !1;
	o
		? ([c, v] = ja(
				() =>
					/** @type {V} */
					e[t]
			))
		: (c = /** @type {V} */ e[t]),
		c === void 0 && n !== void 0 && ((c = u()), d && (i && Oo(), d(c)));
	var b;
	if (
		(i
			? (b = () => {
					var h =
						/** @type {V} */
						e[t];
					return h === void 0 ? u() : ((l = !0), h);
				})
			: (b = () => {
					var h =
						/** @type {V} */
						e[t];
					return h !== void 0 && (a = /** @type {V} */ void 0), h === void 0 ? a : h;
				}),
		i && (r & On) === 0)
	)
		return b;
	if (d) {
		var g = e.$$legacy;
		return function (h, p) {
			return arguments.length > 0 ? ((!i || !p || g || v) && d(p ? b() : h), h) : b();
		};
	}
	var _ = ((r & mo) !== 0 ? ct : Kt)(b);
	return (
		o && E(_),
		function (h, p) {
			if (arguments.length > 0) {
				const w = p ? E(_) : i && o ? ot(h) : h;
				return O(_, w), a !== void 0 && (a = w), h;
			}
			return Ba(_) ? _.v : E(_);
		}
	);
}
function Fa(e) {
	return new Ya(e);
}
var Le, le;
class Ya {
	/**
	 * @param {ComponentConstructorOptions & {
	 *  component: any;
	 * }} options
	 */
	constructor(t) {
		/** @type {any} */
		dr(this, Le);
		/** @type {Record<string, any>} */
		dr(this, le);
		var o;
		var r = /* @__PURE__ */ new Map(),
			n = (s, a) => {
				var l = /* @__PURE__ */ zt(a, !1, !1);
				return r.set(s, l), l;
			};
		const i = new Proxy(
			{ ...(t.props || {}), $$events: {} },
			{
				get(s, a) {
					return E(r.get(a) ?? n(a, Reflect.get(s, a)));
				},
				has(s, a) {
					return a === Pr ? !0 : (E(r.get(a) ?? n(a, Reflect.get(s, a))), Reflect.has(s, a));
				},
				set(s, a, l) {
					return O(r.get(a) ?? n(a, l), l), Reflect.set(s, a, l);
				}
			}
		);
		fr(
			this,
			le,
			(t.hydrate ? wa : _i)(t.component, {
				target: t.target,
				anchor: t.anchor,
				props: i,
				context: t.context,
				intro: t.intro ?? !1,
				recover: t.recover
			})
		),
			(!((o = t == null ? void 0 : t.props) != null && o.$$host) || t.sync === !1) && k(),
			fr(this, Le, i.$$events);
		for (const s of Object.keys(Q(this, le)))
			s === '$set' ||
				s === '$destroy' ||
				s === '$on' ||
				kt(this, s, {
					get() {
						return Q(this, le)[s];
					},
					/** @param {any} value */
					set(a) {
						Q(this, le)[s] = a;
					},
					enumerable: !0
				});
		(Q(this, le).$set =
			/** @param {Record<string, any>} next */
			(s) => {
				Object.assign(i, s);
			}),
			(Q(this, le).$destroy = () => {
				_a(Q(this, le));
			});
	}
	/** @param {Record<string, any>} props */
	$set(t) {
		Q(this, le).$set(t);
	}
	/**
	 * @param {string} event
	 * @param {(...args: any[]) => any} callback
	 * @returns {any}
	 */
	$on(t, r) {
		Q(this, Le)[t] = Q(this, Le)[t] || [];
		const n = (...i) => r.call(this, ...i);
		return (
			Q(this, Le)[t].push(n),
			() => {
				Q(this, Le)[t] = Q(this, Le)[t].filter(
					/** @param {any} fn */
					(i) => i !== n
				);
			}
		);
	}
	$destroy() {
		Q(this, le).$destroy();
	}
}
(Le = new WeakMap()), (le = new WeakMap());
let zi;
typeof HTMLElement == 'function' &&
	(zi = class extends HTMLElement {
		/**
		 * @param {*} $$componentCtor
		 * @param {*} $$slots
		 * @param {*} use_shadow_dom
		 */
		constructor(t, r, n) {
			super();
			/** The Svelte component constructor */
			te(this, '$$ctor');
			/** Slots */
			te(this, '$$s');
			/** @type {any} The Svelte component instance */
			te(this, '$$c');
			/** Whether or not the custom element is connected */
			te(this, '$$cn', !1);
			/** @type {Record<string, any>} Component props data */
			te(this, '$$d', {});
			/** `true` if currently in the process of reflecting component props back to attributes */
			te(this, '$$r', !1);
			/** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */
			te(this, '$$p_d', {});
			/** @type {Record<string, EventListenerOrEventListenerObject[]>} Event listeners */
			te(this, '$$l', {});
			/** @type {Map<EventListenerOrEventListenerObject, Function>} Event listener unsubscribe functions */
			te(this, '$$l_u', /* @__PURE__ */ new Map());
			/** @type {any} The managed render effect for reflecting attributes */
			te(this, '$$me');
			(this.$$ctor = t), (this.$$s = r), n && this.attachShadow({ mode: 'open' });
		}
		/**
		 * @param {string} type
		 * @param {EventListenerOrEventListenerObject} listener
		 * @param {boolean | AddEventListenerOptions} [options]
		 */
		addEventListener(t, r, n) {
			if (((this.$$l[t] = this.$$l[t] || []), this.$$l[t].push(r), this.$$c)) {
				const i = this.$$c.$on(t, r);
				this.$$l_u.set(r, i);
			}
			super.addEventListener(t, r, n);
		}
		/**
		 * @param {string} type
		 * @param {EventListenerOrEventListenerObject} listener
		 * @param {boolean | AddEventListenerOptions} [options]
		 */
		removeEventListener(t, r, n) {
			if ((super.removeEventListener(t, r, n), this.$$c)) {
				const i = this.$$l_u.get(r);
				i && (i(), this.$$l_u.delete(r));
			}
		}
		async connectedCallback() {
			if (((this.$$cn = !0), !this.$$c)) {
				let t = function (i) {
					return (o) => {
						const s = document.createElement('slot');
						i !== 'default' && (s.name = i), I(o, s);
					};
				};
				if ((await Promise.resolve(), !this.$$cn || this.$$c)) return;
				const r = {},
					n = Za(this);
				for (const i of this.$$s)
					i in n &&
						(i === 'default' && !this.$$d.children
							? ((this.$$d.children = t(i)), (r.default = !0))
							: (r[i] = t(i)));
				for (const i of this.attributes) {
					const o = this.$$g_p(i.name);
					o in this.$$d || (this.$$d[o] = qt(o, i.value, this.$$p_d, 'toProp'));
				}
				for (const i in this.$$p_d)
					!(i in this.$$d) && this[i] !== void 0 && ((this.$$d[i] = this[i]), delete this[i]);
				(this.$$c = Fa({
					component: this.$$ctor,
					target: this.shadowRoot || this,
					props: {
						...this.$$d,
						$$slots: r,
						$$host: this
					}
				})),
					(this.$$me = Jo(() => {
						Qt(() => {
							var i;
							this.$$r = !0;
							for (const o of Dt(this.$$c)) {
								if (!((i = this.$$p_d[o]) != null && i.reflect)) continue;
								this.$$d[o] = this.$$c[o];
								const s = qt(o, this.$$d[o], this.$$p_d, 'toAttribute');
								s == null
									? this.removeAttribute(this.$$p_d[o].attribute || o)
									: this.setAttribute(this.$$p_d[o].attribute || o, s);
							}
							this.$$r = !1;
						});
					}));
				for (const i in this.$$l)
					for (const o of this.$$l[i]) {
						const s = this.$$c.$on(i, o);
						this.$$l_u.set(o, s);
					}
				this.$$l = {};
			}
		}
		// We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte
		// and setting attributes through setAttribute etc, this is helpful
		/**
		 * @param {string} attr
		 * @param {string} _oldValue
		 * @param {string} newValue
		 */
		attributeChangedCallback(t, r, n) {
			var i;
			this.$$r ||
				((t = this.$$g_p(t)),
				(this.$$d[t] = qt(t, n, this.$$p_d, 'toProp')),
				(i = this.$$c) == null || i.$set({ [t]: this.$$d[t] }));
		}
		disconnectedCallback() {
			(this.$$cn = !1),
				Promise.resolve().then(() => {
					!this.$$cn && this.$$c && (this.$$c.$destroy(), this.$$me(), (this.$$c = void 0));
				});
		}
		/**
		 * @param {string} attribute_name
		 */
		$$g_p(t) {
			return (
				Dt(this.$$p_d).find(
					(r) =>
						this.$$p_d[r].attribute === t || (!this.$$p_d[r].attribute && r.toLowerCase() === t)
				) || t
			);
		}
	});
function qt(e, t, r, n) {
	var o;
	const i = (o = r[e]) == null ? void 0 : o.type;
	if (((t = i === 'Boolean' && typeof t != 'boolean' ? t != null : t), !n || !r[e])) return t;
	if (n === 'toAttribute')
		switch (i) {
			case 'Object':
			case 'Array':
				return t == null ? null : JSON.stringify(t);
			case 'Boolean':
				return t ? '' : null;
			case 'Number':
				return t ?? null;
			default:
				return t;
		}
	else
		switch (i) {
			case 'Object':
			case 'Array':
				return t && JSON.parse(t);
			case 'Boolean':
				return t;
			// conversion already handled above
			case 'Number':
				return t != null ? +t : t;
			default:
				return t;
		}
}
function Za(e) {
	const t = {};
	return (
		e.childNodes.forEach((r) => {
			t[
				/** @type {Element} node */
				r.slot || 'default'
			] = !0;
		}),
		t
	);
}
function R(e, t, r, n, i, o) {
	let s = class extends zi {
		constructor() {
			super(e, r, i), (this.$$p_d = t);
		}
		static get observedAttributes() {
			return Dt(t).map((a) => (t[a].attribute || a).toLowerCase());
		}
	};
	return (
		Dt(t).forEach((a) => {
			kt(s.prototype, a, {
				get() {
					return this.$$c && a in this.$$c ? this.$$c[a] : this.$$d[a];
				},
				set(l) {
					var f;
					(l = qt(a, l, t)), (this.$$d[a] = l);
					var u = this.$$c;
					if (u) {
						var d = (f = Ue(u, a)) == null ? void 0 : f.get;
						d ? (u[a] = l) : u.$set({ [a]: l });
					}
				}
			});
		}),
		n.forEach((a) => {
			kt(s.prototype, a, {
				get() {
					var l;
					return (l = this.$$c) == null ? void 0 : l[a];
				}
			});
		}),
		(e.element = /** @type {any} */ s),
		s
	);
}
var Ja = /* @__PURE__ */ be('<a><!></a>'),
	Wa = /* @__PURE__ */ be('<button><!></button>');
const Ka = {
	hash: 'svelte-zftwkm',
	code: `:root, :host {
    /* global button */--ai-button-radius: var(--ai-radius-round);--ai-button-padding-x: 2rem;--ai-button-padding-y: 0.75rem;--ai-button-height: 2.5rem;--ai-button-box-shadow: inset 0 0 0 1px;

    /* primary button */--ai-button-background-primary-rest: var(--ai-color-black);--ai-button-background-primary-hover: var(
      --ai-color-neutral-900
    );--ai-button-background-primary-disabled: var(
      --ai-color-neutral-200
    );--ai-button-text-primary-rest: var(--ai-color-white);--ai-button-text-primary-hover: var(--ai-button-text-primary-rest);--ai-button-text-primary-disabled: var(--ai-color-neutral-800);

    /* inverted button */--ai-button-background-inverted-rest: var(--ai-color-white);--ai-button-background-inverted-hover: var(--ai-color-neutral-200);--ai-button-background-inverted-disabled: var(--ai-color-neutral-500);--ai-button-text-inverted-rest: var(--ai-color-black);--ai-button-text-inverted-hover: var(--ai-color-black);--ai-button-text-inverted-disabled: var(--ai-color-neutral-900);--ai-button-border-inverted-rest: none;--ai-button-border-inverted-hover: none;

    /* unstyled */--ai-button-text-unstyled-disabled: var(--ai-color-neutral-700);--ai-button-text-unstyled-inverted-disabled: var(--ai-color-neutral-700);

    /* outline button */--ai-button-background-outline-hover: var(--ai-color-black);--ai-button-text-outline-rest: var(--ai-color-black);--ai-button-text-outline-hover: var(--ai-color-white);--ai-button-border-outline-rest: var(--ai-color-black);--ai-button-border-outline-hover: var(--ai-button-border-outline-rest);

    /* outline inverted button */--ai-button-background-outline-inverted-hover: var(--ai-color-white);--ai-button-text-outline-inverted-rest: var(--ai-color-white);--ai-button-text-outline-inverted-hover: var(--ai-color-black);--ai-button-border-outline-inverted-rest: var(--ai-color-white);--ai-button-border-outline-inverted-hover: var(
      --ai-button-border-outline-inverted-rest
    );}.ai-button.svelte-zftwkm {background-color:var(--ai-button-background-primary-rest);border-radius:var(--ai-button-radius);color:var(--ai-button-text-primary-rest);font-weight:var(--ai-font-weight-medium);padding-inline:var(--ai-button-padding-x);padding-block:var(--ai-button-padding-y);margin:0;}.ai-button.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-primary-hover);color:var(--ai-button-text-primary-hover);}.ai-button--small.svelte-zftwkm {--ai-button-padding-x: var(--ai-size-24);--ai-button-padding-y: var(--ai-size-8);font-size:var(--ai-size-12);}.ai-button--outline.svelte-zftwkm {background-color:transparent;box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-rest);color:var(--ai-button-text-outline-rest);}.ai-button--outline.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-outline-hover);box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-hover);color:var(--ai-button-text-outline-hover);}.ai-button--outline-inverted.svelte-zftwkm {background-color:transparent;box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-inverted-rest);color:var(--ai-button-text-outline-inverted-rest);}.ai-button--outline-inverted.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-outline-inverted-hover);box-shadow:var(--ai-button-box-shadow) var(--ai-button-border-outline-inverted-hover);color:var(--ai-button-text-outline-inverted-hover);}.ai-button--inverted.svelte-zftwkm {background-color:var(--ai-button-background-inverted-rest);color:var(--ai-button-text-inverted-rest);box-shadow:none;border:var(--ai-button-border-inverted-rest);}.ai-button--inverted.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background-color:var(--ai-button-background-inverted-hover);color:var(--ai-button-text-inverted-hover);border:var(--ai-button-border-inverted-hover);}.ai-button--unstyled.svelte-zftwkm {background:none;color:inherit;border:none;box-shadow:none;padding:0;font:inherit;cursor:pointer;text-decoration:underline;text-underline-offset:var(--ai-font-underline-offset);}.ai-button--unstyled.svelte-zftwkm:is(:where(.svelte-zftwkm):hover, :where(.svelte-zftwkm):focus) {background:none;color:inherit;border:none;box-shadow:none;text-decoration:underline;}.ai-menu-buttons.svelte-zftwkm {display:flex;gap:var(--ai-size-16);}

  /* disabled button styles */:is(.disabled.svelte-zftwkm, [disabled].svelte-zftwkm, [aria-disabled=true].svelte-zftwkm) {background:var(--ai-button-background-primary-disabled);color:var(--ai-button-text-primary-disabled);cursor:not-allowed;}.ai-button--inverted.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {background:var(--ai-button-background-inverted-disabled);color:var(--ai-button-text-inverted-disabled);}.ai-button--outline.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {background:transparent;box-shadow:var(--ai-button-box-shadow) var(--ai-color-neutral-700);color:var(--ai-color-neutral-700);}.ai-button--outline-inverted.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {box-shadow:var(--ai-button-box-shadow) var(--ai-color-neutral-200);color:var(--ai-color-neutral-600);}.ai-button--unstyled.svelte-zftwkm:is(.disabled:where(.svelte-zftwkm), [disabled]:where(.svelte-zftwkm), [aria-disabled=true]:where(.svelte-zftwkm)) {background:none;color:inherit;opacity:0.75;}`
};
function Mi(e, t) {
	B(t, !0), bt(e, Ka);
	let r = y(t, 'variant', 7, 'primary'),
		n = y(t, 'theme', 7, 'default'),
		i = y(t, 'href', 7, null),
		o = y(t, 'disabled', 7, !1),
		s = y(t, 'onclick', 7),
		a = y(t, 'children', 7, null),
		l = y(t, 'size', 7, 'medium'),
		u = y(t, 'type', 7, 'button'),
		d = y(t, 'class', 7, ''),
		f = /* @__PURE__ */ Br(t, [
			'$$slots',
			'$$events',
			'$$legacy',
			'$$host',
			'variant',
			'theme',
			'href',
			'disabled',
			'onclick',
			'children',
			'size',
			'type',
			'class'
		]);
	const c = /* @__PURE__ */ J(
			() => () =>
				[
					'usa-button',
					'ai-button',
					l() === 'small' ? 'ai-button--small' : '',
					n() === 'inverted' && r() === 'primary' ? 'ai-button--inverted' : '',
					n() === 'inverted' && r() === 'secondary' ? 'ai-button--outline-inverted' : '',
					r() === 'primary' ? 'usa-button--primary' : '',
					r() === 'secondary' ? 'usa-button--outline ai-button--outline' : '',
					r() === 'unstyled' ? 'ai-button--unstyled' : ''
				]
					.filter(Boolean)
					.join(' ')
		),
		v = /* @__PURE__ */ J(() => () => `${E(c)()} ${d()}`.trim());
	var b = Lt(),
		g = Ye(b);
	{
		var _ = (h) => {
				var p = Ja();
				Ft(
					p,
					(x, M) => ({
						class: x,
						href: i(),
						onclick: s(),
						...f,
						[it]: M
					}),
					[() => E(v)(), () => ({ disabled: o() })],
					'svelte-zftwkm'
				);
				var w = S(p);
				Cr(w, () => a() ?? ve), C(p), I(h, p);
			},
			m = (h) => {
				var p = Wa();
				Ft(
					p,
					(x) => ({
						class: x,
						type: u(),
						disabled: o(),
						onclick: s(),
						...f
					}),
					[() => E(v)()],
					'svelte-zftwkm'
				);
				var w = S(p);
				Cr(w, () => a() ?? ve), C(p), I(h, p);
			};
		de(g, (h) => {
			i() ? h(_) : h(m, !1);
		});
	}
	return (
		I(e, b),
		F({
			get variant() {
				return r();
			},
			set variant(h = 'primary') {
				r(h), k();
			},
			get theme() {
				return n();
			},
			set theme(h = 'default') {
				n(h), k();
			},
			get href() {
				return i();
			},
			set href(h = null) {
				i(h), k();
			},
			get disabled() {
				return o();
			},
			set disabled(h = !1) {
				o(h), k();
			},
			get onclick() {
				return s();
			},
			set onclick(h) {
				s(h), k();
			},
			get children() {
				return a();
			},
			set children(h = null) {
				a(h), k();
			},
			get size() {
				return l();
			},
			set size(h = 'medium') {
				l(h), k();
			},
			get type() {
				return u();
			},
			set type(h = 'button') {
				u(h), k();
			},
			get class() {
				return d();
			},
			set class(h = '') {
				d(h), k();
			}
		})
	);
}
R(
	Mi,
	{
		variant: {},
		theme: {},
		href: {},
		disabled: {},
		onclick: {},
		children: {},
		size: {},
		type: {},
		class: {}
	},
	[],
	[],
	!0
);
function Ga(e, t) {
	B(t, !0);
	let r = y(t, 'variant', 7, 'primary'),
		n = y(t, 'theme', 7, 'default'),
		i = y(t, 'href', 7, null),
		o = y(t, 'disabled', 7, !1),
		s = y(t, 'size', 7, 'medium'),
		a = y(t, 'type', 7, 'button'),
		l = y(t, 'onclick', 7),
		u = y(t, 'children', 7, null),
		d = y(t, 'class', 7, ''),
		f = /* @__PURE__ */ Br(t, [
			'$$slots',
			'$$events',
			'$$legacy',
			'$$host',
			'variant',
			'theme',
			'href',
			'disabled',
			'size',
			'type',
			'onclick',
			'children',
			'class'
		]);
	return (
		Mi(
			e,
			Ti(
				{
					get variant() {
						return r();
					},
					get theme() {
						return n();
					},
					get href() {
						return i();
					},
					get disabled() {
						return o();
					},
					get size() {
						return s();
					},
					get type() {
						return a();
					},
					get onclick() {
						return l();
					},
					get children() {
						return u();
					},
					get class() {
						return d();
					}
				},
				() => f,
				{
					$$slots: {
						default: (c, v) => {
							var b = Lt(),
								g = Ye(b);
							ir(g, t, 'default', {}), I(c, b);
						}
					}
				}
			)
		),
		F({
			get variant() {
				return r();
			},
			set variant(c = 'primary') {
				r(c), k();
			},
			get theme() {
				return n();
			},
			set theme(c = 'default') {
				n(c), k();
			},
			get href() {
				return i();
			},
			set href(c = null) {
				i(c), k();
			},
			get disabled() {
				return o();
			},
			set disabled(c = !1) {
				o(c), k();
			},
			get size() {
				return s();
			},
			set size(c = 'medium') {
				s(c), k();
			},
			get type() {
				return a();
			},
			set type(c = 'button') {
				a(c), k();
			},
			get onclick() {
				return l();
			},
			set onclick(c) {
				l(c), k();
			},
			get children() {
				return u();
			},
			set children(c = null) {
				u(c), k();
			},
			get class() {
				return d();
			},
			set class(c = '') {
				d(c), k();
			}
		})
	);
}
customElements.define(
	'graymatter-button',
	R(
		Ga,
		{
			variant: { attribute: 'variant', reflect: !0, type: 'String' },
			theme: { attribute: 'theme', reflect: !0, type: 'String' },
			href: { attribute: 'href', reflect: !0, type: 'String' },
			disabled: { attribute: 'disabled', reflect: !0, type: 'Boolean' },
			size: { attribute: 'size', reflect: !0, type: 'String' },
			type: { attribute: 'type', reflect: !0, type: 'String' },
			onclick: {},
			children: {},
			class: {}
		},
		['default'],
		[],
		!0
	)
);
const At = typeof window < 'u';
function Xa(e, t) {
	if (At && typeof sessionStorage < 'u') {
		const r = sessionStorage.getItem(e);
		if (r !== null)
			try {
				return JSON.parse(r);
			} catch (n) {
				return console.warn(`Failed to parse stored value for ${e}:`, n), t;
			}
	}
	return t;
}
function Li(e, t) {
	const r = Xa(e, t),
		n = or(r);
	return (
		At &&
			n.subscribe((i) => {
				try {
					sessionStorage.setItem(e, JSON.stringify(i));
				} catch (o) {
					console.warn(`Failed to persist ${e} to sessionStorage:`, o);
				}
			}),
		n
	);
}
Li('subNav-selectedId', null);
const Xe = Li('subNav-selectedTitle', '');
function Ui(e, t) {
	if (At && typeof localStorage < 'u') {
		const r = localStorage.getItem(e);
		if (r !== null)
			try {
				return JSON.parse(r);
			} catch (n) {
				return console.warn(`Failed to parse stored value for ${e}:`, n), t;
			}
	}
	return t;
}
function Ai(e, t) {
	const r = Ui(e, t),
		n = or(r);
	return (
		At &&
			n.subscribe((i) => {
				try {
					localStorage.setItem(e, JSON.stringify(i));
				} catch (o) {
					console.warn(`Failed to persist ${e} to localStorage:`, o);
				}
			}),
		n
	);
}
const me = Ai('globalSideNav-isExpanded', !1),
	tt = Ai('globalSideNav-selectedItem', null),
	Fr = Pa(tt, (e) => e === 'discover'),
	Ie = {
		toggleExpanded: () => {
			me.update((e) => !e);
		},
		setExpanded: (e) => {
			me.set(e);
		},
		setSelectedItem: (e) => {
			tt.set(e), e === 'discover' ? me.set(!1) : At && window.innerWidth > 640 && me.set(!0);
		},
		handleLayoutTransition: (e) => {
			if (e) me.set(!1);
			else {
				const t = Ui('globalSideNav-selectedItem', null);
				t && t !== 'discover'
					? (console.log('[NavigationStore] Opening navigation for desktop (non-discover page)'),
						me.set(!0))
					: me.set(!1);
			}
		}
	},
	Zt = /* @__PURE__ */ Object.freeze(
		/* @__PURE__ */ Object.defineProperty(
			{
				__proto__: null,
				isExpanded: me,
				isMenuButtonDisabled: Fr,
				navigationStore: Ie,
				selectedItem: tt,
				selectedSubNavItemTitle: Xe
			},
			Symbol.toStringTag,
			{ value: 'Module' }
		)
	),
	Qa = or(!1);
var es = /* @__PURE__ */ be(
	'<div part="global-header"><div class="header-content svelte-1p8bjnn" part="header-content"><span class="header-text svelte-1p8bjnn"> </span></div></div>'
);
const ts = {
	hash: 'svelte-1p8bjnn',
	code: '.global-header.svelte-1p8bjnn {opacity:0;transition:opacity 0.2s;}.global-header.visible.svelte-1p8bjnn {opacity:1;transition:opacity 0.2s;}.header-content.svelte-1p8bjnn {display:flex;align-items:center;gap:var(--ai-size-16);background:var(--ai-color-white);height:var(--ai-size-56);padding:0 5.8rem;}.header-content.svelte-1p8bjnn {display:flex;align-items:center;gap:var(--ai-size-16);align-self:stretch;}.header-text.svelte-1p8bjnn {color:var(--ai-color-black);font-size:var(--ai-size-16);font-weight:400;line-height:1.3;}'
};
function Oi(e, t) {
	B(t, !0), bt(e, ts);
	const [r, n] = Ut(),
		i = () => fe(tt, '$selectedItem', r),
		o = () => fe(me, '$isExpanded', r),
		s = () => fe(Qa, '$subNavReady', r),
		a = () => fe(Xe, '$selectedSubNavItemTitle', r),
		l = y(t, 'customHeaderText', 7, '');
	let u = !1;
	const d = /* @__PURE__ */ J(() => () => i() && i() !== 'discover' && !o() && !u && s()),
		f = /* @__PURE__ */ J(() => () => l() || a());
	nr(() => {
		const h = (w) => {
				if (typeof window < 'u' && window.innerWidth <= 640) return;
				let x = null;
				if (typeof window < 'u' && ((x = sessionStorage.getItem('subNav-topLevelTitle')), x))
					try {
						x = JSON.parse(x);
					} catch {}
				if (typeof x == 'string' && x.trim() !== '')
					Xe.set(x), sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(x));
				else {
					const M = w;
					M.detail &&
						M.detail.title &&
						(Xe.set(M.detail.title),
						sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(M.detail.title)));
				}
			},
			p = (w) => {
				const x = w,
					{ isMobile: M } = x.detail;
				M ||
					((u = !0),
					setTimeout(() => {
						u = !1;
					}, 300));
			};
		return (
			window.addEventListener('sectionVisible', h),
			window.addEventListener('layoutTransition', p),
			() => {
				window.removeEventListener('sectionVisible', h),
					window.removeEventListener('layoutTransition', p);
			}
		);
	});
	var c = es();
	let v;
	var b = S(c),
		g = S(b),
		_ = S(g, !0);
	C(g),
		C(b),
		C(c),
		ft(
			(h, p) => {
				(v = ue(c, 1, 'global-header svelte-1p8bjnn', null, v, h)), Vr(_, p);
			},
			[() => ({ visible: E(d)() }), () => E(f)()]
		),
		I(e, c);
	var m = F({
		get customHeaderText() {
			return l();
		},
		set customHeaderText(h = '') {
			l(h), k();
		}
	});
	return n(), m;
}
R(Oi, { customHeaderText: {} }, [], [], !0);
function rs(e, t) {
	B(t, !0);
	let r = y(t, 'customHeaderText', 7, '');
	return (
		Oi(e, {
			get customHeaderText() {
				return r();
			}
		}),
		F({
			get customHeaderText() {
				return r();
			},
			set customHeaderText(n = '') {
				r(n), k();
			}
		})
	);
}
customElements.define(
	'graymatter-desktop-header',
	R(
		rs,
		{
			customHeaderText: {
				attribute: 'custom-header-text',
				reflect: !0,
				type: 'String'
			}
		},
		[],
		[],
		!0
	)
);
Ho();
var ns = /* @__PURE__ */ j(
		'<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7883 3.2108C11.2367 2.13286 12.7637 2.13286 13.212 3.2108L15.294 8.21652L20.6981 8.64976C21.8619 8.74306 22.3337 10.1953 21.4471 10.9549L17.3298 14.4818L18.5877 19.7553C18.8585 20.8909 17.6232 21.7884 16.6268 21.1799L12.0002 18.354L7.37353 21.1799C6.37721 21.7884 5.14182 20.8909 5.4127 19.7553L6.67062 14.4818L2.55328 10.9549C1.66664 10.1953 2.13851 8.74306 3.30224 8.64976L8.70633 8.21652L10.7883 3.2108Z" fill="black"></path>'
	),
	is = /* @__PURE__ */ j(
		'<path d="M11.4807 3.49883C11.6729 3.03685 12.3273 3.03685 12.5195 3.49883L14.6454 8.61028C14.7264 8.80504 14.9096 8.93811 15.1199 8.95497L20.6381 9.39736C21.1369 9.43735 21.3391 10.0598 20.9591 10.3853L16.7548 13.9867C16.5946 14.1239 16.5246 14.3392 16.5736 14.5444L17.858 19.9293C17.9741 20.416 17.4447 20.8007 17.0177 20.5398L12.2933 17.6542C12.1133 17.5443 11.8869 17.5443 11.7069 17.6542L6.98251 20.5398C6.55551 20.8007 6.02606 20.416 6.14215 19.9293L7.42664 14.5444C7.47558 14.3392 7.40562 14.1239 7.24543 13.9867L3.04111 10.3853C2.66112 10.0598 2.86335 9.43735 3.36209 9.39736L8.88034 8.95497C9.0906 8.93811 9.27375 8.80504 9.35476 8.61028L11.4807 3.49883Z" stroke="#585C63" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>'
	),
	os = /* @__PURE__ */ j(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function Yr(e, t) {
	B(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = os(),
		i = S(n);
	{
		var o = (a) => {
				var l = ns();
				I(a, l);
			},
			s = (a) => {
				var l = is();
				I(a, l);
			};
		de(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		C(n),
		I(e, n),
		F({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), k();
			}
		})
	);
}
R(Yr, { isSelected: {} }, [], [], !0);
var as = /* @__PURE__ */ j(
		'<path fill-rule="evenodd" clip-rule="evenodd" d="M17.0508 3C19.6009 3.00039 21.6688 5.06802 21.6689 7.61816V13.2832C21.6688 15.8333 19.6009 17.901 17.0508 17.9014H13.2686L9.23926 21.7168C8.58852 22.3323 7.5166 21.8705 7.5166 20.9746V17.8984C5.01299 17.8445 3.00011 15.7997 3 13.2832V7.61816C3.00012 5.06785 5.06785 3.00012 7.61816 3H17.0508Z" fill="black"></path>'
	),
	ss = /* @__PURE__ */ j(
		'<path d="M17.0508 3C19.6009 3.00039 21.6688 5.06802 21.6689 7.61816V13.2832C21.6688 15.8333 19.6009 17.901 17.0508 17.9014H13.2686L9.23926 21.7168C8.58852 22.3323 7.5166 21.8705 7.5166 20.9746V17.8984C5.01299 17.8445 3.00011 15.7997 3 13.2832V7.61816C3.00012 5.06785 5.06785 3.00012 7.61816 3H17.0508ZM7.61816 4.45898C5.87327 4.4591 4.4591 5.87327 4.45898 7.61816V13.2832C4.4591 15.0281 5.87327 16.4432 7.61816 16.4434L8.24609 16.4424C8.64868 16.4424 8.97539 16.7693 8.97559 17.1719V19.958L12.4775 16.6426C12.6186 16.5091 12.7994 16.4443 12.9795 16.4443V16.4434H17.0508C18.7954 16.443 20.2099 15.0279 20.21 13.2832V7.61816C20.2098 5.87344 18.7954 4.45938 17.0508 4.45898H7.61816Z" fill="#585C63"></path>'
	),
	ls = /* @__PURE__ */ j(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function Zr(e, t) {
	B(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = ls(),
		i = S(n);
	{
		var o = (a) => {
				var l = as();
				I(a, l);
			},
			s = (a) => {
				var l = ss();
				I(a, l);
			};
		de(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		C(n),
		I(e, n),
		F({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), k();
			}
		})
	);
}
R(Zr, { isSelected: {} }, [], [], !0);
var us = /* @__PURE__ */ j(
		'<rect x="2.75" y="6.02856" width="3.7459" height="12.9262" fill="black" stroke="black" stroke-width="1.5"></rect><rect x="9.96338" y="9.96313" width="3.7459" height="8.9918" fill="black" stroke="black" stroke-width="1.5"></rect><rect x="17.1763" y="2.75" width="3.7459" height="16.2049" fill="black" stroke="black" stroke-width="1.5"></rect><path d="M2.32764 22H21.344" stroke="black" stroke-width="1.5" stroke-linecap="square"></path>',
		1
	),
	cs = /* @__PURE__ */ j(
		'<rect x="2.75" y="6.02856" width="3.7459" height="12.9262" stroke="#585C63" stroke-width="1.5"></rect><rect x="9.96338" y="9.96313" width="3.7459" height="8.9918" stroke="#585C63" stroke-width="1.5"></rect><rect x="17.1763" y="2.75" width="3.7459" height="16.2049" stroke="#585C63" stroke-width="1.5"></rect><path d="M2.32764 22H21.344" stroke="#585C63" stroke-width="1.5" stroke-linecap="square"></path>',
		1
	),
	ds = /* @__PURE__ */ j(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function Jr(e, t) {
	B(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = ds(),
		i = S(n);
	{
		var o = (a) => {
				var l = us();
				Ne(3), I(a, l);
			},
			s = (a) => {
				var l = cs();
				Ne(3), I(a, l);
			};
		de(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		C(n),
		I(e, n),
		F({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), k();
			}
		})
	);
}
R(Jr, { isSelected: {} }, [], [], !0);
var fs = /* @__PURE__ */ j(
		'<rect x="4.41699" y="4.62451" width="15.1664" height="15.1664" rx="1.75" stroke="black" stroke-width="1.5"></rect><g><path d="M9.5 3.66664V2" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 14.4998L2 14.4998" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 14.4998L20.3335 14.4998" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M9.5 21.9999V20.3333" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 3.66664V2" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 12.0005L2 12.0005" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 12.0005L20.3335 12.0005" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 21.9999V20.3333" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 3.66664V2" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 9.5L2 9.5" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 9.5L20.3335 9.5" stroke="black" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 21.9999V20.3333" stroke="black" stroke-width="1.5" stroke-linecap="round"></path></g><path d="M10.5 8.52856H13.7559C14.7224 8.52856 15.5059 9.31207 15.5059 10.2786V13.5344C15.5059 14.5009 14.7224 15.2844 13.7559 15.2844H10.5C9.5335 15.2844 8.75 14.5009 8.75 13.5344V10.2786C8.75 9.31207 9.5335 8.52856 10.5 8.52856Z" fill="black" stroke="black" stroke-width="1.5"></path>',
		1
	),
	vs = /* @__PURE__ */ j(
		'<rect x="4.41699" y="4.62451" width="15.1664" height="15.1664" rx="1.75" stroke="#585C63" stroke-width="1.5"></rect><g><path d="M9.5 3.66664V2" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 14.4995L2 14.4995" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 14.4995L20.3335 14.4995" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M9.5 21.9999V20.3333" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 3.66664V2" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 12.0002L2 12.0002" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 12.0002L20.3335 12.0002" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M12.0005 21.9999V20.3333" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 3.66664V2" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M3.66664 9.49976L2 9.49976" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M22.0001 9.49976L20.3335 9.49976" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path><path d="M14.4995 21.9999V20.3333" stroke="#585C63" stroke-width="1.5" stroke-linecap="round"></path></g><rect x="8.52881" y="8.52856" width="6.75594" height="6.75594" rx="1.75" stroke="#585C63" stroke-width="1.5"></rect>',
		1
	),
	hs = /* @__PURE__ */ j(
		'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><!></svg>'
	);
function Wr(e, t) {
	B(t, !1);
	let r = y(t, 'isSelected', 12, !1);
	var n = hs(),
		i = S(n);
	{
		var o = (a) => {
				var l = fs();
				Ne(2), I(a, l);
			},
			s = (a) => {
				var l = vs();
				Ne(2), I(a, l);
			};
		de(i, (a) => {
			r() ? a(o) : a(s, !1);
		});
	}
	return (
		C(n),
		I(e, n),
		F({
			get isSelected() {
				return r();
			},
			set isSelected(a) {
				r(a), k();
			}
		})
	);
}
R(Wr, { isSelected: {} }, [], [], !0);
var bs = /* @__PURE__ */ j(
		'<path d="M18.75 3H5.25C4.00736 3 3 4.09719 3 5.45064V7.90129V18.5207C3 19.8742 4.00736 20.9714 5.25 20.9714H18.75C19.9926 20.9714 21 19.8742 21 18.5207V7.90129V5.45064C21 4.09719 19.9926 3 18.75 3Z" stroke="#C7CDD6" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path stroke="#C7CDD6" stroke-width="1.5"></path>',
		1
	),
	ps = /* @__PURE__ */ j(
		'<path d="M12.25 18L6 12M6 12L12.25 6M6 12L21 12" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3 3V21" stroke="black" stroke-width="1.5" stroke-linecap="round"></path>',
		1
	),
	gs = /* @__PURE__ */ j(
		'<path d="M11.75 18.25L18 12M18 12L11.75 5.75M18 12L3 12" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M21 3V21" stroke="black" stroke-width="1.5" stroke-linecap="round"></path>',
		1
	),
	ms = /* @__PURE__ */ j(
		'<path d="M8.18018 5.45068V18.5208" stroke="#585C63" stroke-width="1.5"></path>'
	),
	ws = /* @__PURE__ */ j(
		'<path d="M9.18018 3.27222V20.9203" stroke="#585C63" stroke-width="1.5"></path>'
	),
	_s = /* @__PURE__ */ j(
		'<path d="M18.75 3H5.25C4.00736 3 3 4.09719 3 5.45064V7.90129V18.5207C3 19.8742 4.00736 20.9714 5.25 20.9714H18.75C19.9926 20.9714 21 19.8742 21 18.5207V7.90129V5.45064C21 4.09719 19.9926 3 18.75 3Z" stroke="#585C63" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><!>',
		1
	),
	ks = /* @__PURE__ */ j('<svg><!></svg>');
function Kr(e, t) {
	const r = Yt(t, ['children', '$$slots', '$$events', '$$legacy', '$$host']),
		n = Yt(r, ['isDisabled', 'isHovered', 'isOpen']);
	B(t, !1);
	let i = y(t, 'isDisabled', 12, !1),
		o = y(t, 'isHovered', 12, !1),
		s = y(t, 'isOpen', 12, !1);
	var a = ks();
	Ft(a, () => ({
		xmlns: 'http://www.w3.org/2000/svg',
		width: '24',
		height: '24',
		viewBox: '0 0 24 24',
		fill: 'none',
		...n
	}));
	var l = S(a);
	{
		var u = (f) => {
				var c = bs(),
					v = W(Ye(c));
				ft(() =>
					G(
						v,
						'd',
						`M${s() ? '8.18018' : '9.18018'} ${s() ? '5.45068' : '3.27222'}V${s() ? '18.5208' : '20.9203'}`
					)
				),
					I(f, c);
			},
			d = (f, c) => {
				{
					var v = (g) => {
							var _ = Lt(),
								m = Ye(_);
							{
								var h = (w) => {
										var x = ps();
										Ne(), I(w, x);
									},
									p = (w) => {
										var x = gs();
										Ne(), I(w, x);
									};
								de(m, (w) => {
									s() ? w(h) : w(p, !1);
								});
							}
							I(g, _);
						},
						b = (g) => {
							var _ = _s(),
								m = W(Ye(_));
							{
								var h = (w) => {
										var x = ms();
										I(w, x);
									},
									p = (w) => {
										var x = ws();
										I(w, x);
									};
								de(m, (w) => {
									s() ? w(h) : w(p, !1);
								});
							}
							I(g, _);
						};
					de(
						f,
						(g) => {
							o() ? g(v) : g(b, !1);
						},
						c
					);
				}
			};
		de(l, (f) => {
			i() ? f(u) : f(d, !1);
		});
	}
	return (
		C(a),
		I(e, a),
		F({
			get isDisabled() {
				return i();
			},
			set isDisabled(f) {
				i(f), k();
			},
			get isHovered() {
				return o();
			},
			set isHovered(f) {
				o(f), k();
			},
			get isOpen() {
				return s();
			},
			set isOpen(f) {
				s(f), k();
			}
		})
	);
}
R(Kr, { isDisabled: {}, isHovered: {}, isOpen: {} }, [], [], !0);
const ys = {};
function pr(e, t) {
	t || console.warn(`[getAppUrls] Environment variable for ${e} is missing!`);
}
function qi(e) {
	const t = ys,
		r = '#',
		n = t.PUBLIC_CHAT_URL,
		i = t.PUBLIC_CONSOLE_URL,
		o = t.PUBLIC_API_URL,
		s = t.PUBLIC_DISCOVER_URL;
	return (
		pr('chat', n),
		pr('console', i),
		pr('api', o),
		{
			chat: n || r,
			console: i || r,
			api: o || '/api',
			discover: s || '/discover'
		}
	);
}
function xs(e, t, r, n) {
	var i;
	t() || (Ie.toggleExpanded(), (i = r()) == null || i({ expanded: n() }));
}
var Es =
		// Set navigation flag immediately to prevent header restoration
		// Clear header state for main navigation items (including discover)
		// Clear the store value immediately
		// Set default header from first submenu item after a short delay
		// Delay to ensure page content is loaded
		// Clear header state for Discover page
		(e, t) => t('discover'),
	Cs = (e, t) => t(e, 'discover'),
	Ss = (e, t) => t('chat'),
	$s = (e, t) => t(e, 'chat'),
	Ns = (e, t) => t('console'),
	Is = (e, t) => t(e, 'console'),
	Ts = (e, t) => t('api'),
	zs = (e, t) => t(e, 'api'),
	Ms = (e, t) => {
		var r;
		return (r = t()) == null ? void 0 : r();
	},
	Ls = (e, t) => {
		var r;
		(e.key === 'Enter' || e.key === ' ') && (e.preventDefault(), (r = t()) == null || r());
	},
	Us = /* @__PURE__ */ be(
		'<div class="drawer-content svelte-14q02bh" part="drawer-content"><div class="logo-placeholder svelte-14q02bh" part="logo-placeholder"></div> <nav aria-label="Sub navigation" part="sub-navigation"><!></nav></div>'
	),
	As = /* @__PURE__ */ be(
		'<div part="side-nav-container"><div class="nav-content svelte-14q02bh" part="nav-content"><div class="nav-main svelte-14q02bh" part="nav-main"><div class="menu-section svelte-14q02bh" part="menu-section"><button><div class="icon-container svelte-14q02bh"><div class="icon-wrapper svelte-14q02bh"><!></div></div></button></div> <div class="nav-items svelte-14q02bh" part="nav-items"><a part="discover-item"><div class="icon-container svelte-14q02bh"><div class="icon-wrapper svelte-14q02bh"><!></div></div> <span class="nav-text svelte-14q02bh">Discover</span></a> <a part="chat-item"><div class="icon-container svelte-14q02bh"><div class="icon-wrapper svelte-14q02bh"><!></div></div> <span class="nav-text svelte-14q02bh">Chat</span></a> <a part="console-item"><div class="icon-container svelte-14q02bh"><div class="icon-wrapper svelte-14q02bh"><!></div></div> <span class="nav-text svelte-14q02bh">Console</span></a> <a part="api-item"><div class="icon-container svelte-14q02bh"><div class="icon-wrapper svelte-14q02bh"><!></div></div> <span class="nav-text svelte-14q02bh">API</span></a></div></div> <div class="nav-footer svelte-14q02bh" part="nav-footer"><button class="user-profile-button svelte-14q02bh" tabindex="0"><div class="user-profile-container svelte-14q02bh"><div class="user-profile-icon svelte-14q02bh" part="user-profile-icon"><span class="user-initials svelte-14q02bh">ZW</span></div></div></button></div></div> <div part="expanded-content"><!></div></div>'
	);
const Os = {
	hash: 'svelte-14q02bh',
	code: `.expanded-content.drawer-anim.svelte-14q02bh {max-width:0;overflow:hidden;pointer-events:none;transition:max-width 0.2s cubic-bezier(.4, 0, .2, 1);}.expanded-content.drawer-anim.open.svelte-14q02bh {max-width:12.5rem;pointer-events:auto;}.drawer-content.svelte-14q02bh {width:12.5rem;height:100%;}.logo-placeholder.svelte-14q02bh {height:var(--ai-size-72);}.user-profile-container.svelte-14q02bh,
  .user-profile-icon.svelte-14q02bh {display:flex;align-items:center;justify-content:center;}.menu-button.svelte-14q02bh {display:flex;align-items:center;justify-content:center;border:none;background:none;cursor:pointer;border-radius:var(--ai-size-8);transition:background-color 0.2s ease;}.menu-button.svelte-14q02bh:disabled,
  .menu-button.disabled.svelte-14q02bh {cursor:not-allowed;opacity:0.5;background-color:transparent;}.user-profile-icon.svelte-14q02bh {border-radius:var(--ai-size-6);background:var(--ai-color-steel-700);}.user-initials.svelte-14q02bh {color:var(--ai-color-white);text-align:center;font-weight:400;line-height:1.3;}.side-nav-container.svelte-14q02bh {display:flex;flex-shrink:0;position:relative;z-index:var(--ai-layer-2);height:100%;}.side-nav-container.expanded.svelte-14q02bh {width:16.875rem;min-width:12.5rem;max-width:16.875rem;height:100%;}.expanded-content.svelte-14q02bh {display:flex;width:12.5rem;opacity:1;flex:none;height:100%;background:var(--ai-color-steel-100);border-right:0.0625rem solid var(--ai-color-steel-100);overflow:hidden;flex-direction:column;}.side-nav-container.expanded.svelte-14q02bh .expanded-content:where(.svelte-14q02bh) {width:12.5rem;opacity:1;overflow-y:auto;}.side-nav-container.svelte-14q02bh:not(.expanded) .expanded-content:where(.svelte-14q02bh) {width:0;opacity:0;}.nav-content.svelte-14q02bh {display:flex;width:var(--ai-size-72);height:100%;padding-bottom:var(--ai-size-16);flex-direction:column;align-items:flex-start;gap:var(--ai-size-4);flex:none;background:var(--ai-color-steel-200);border-right:0.0625rem solid var(--ai-color-steel-200);}.nav-main.svelte-14q02bh {display:flex;flex-direction:column;align-items:flex-start;gap:var(--ai-size-4);flex:1 0 0;align-self:stretch;}.nav-footer.svelte-14q02bh {display:flex;padding:var(--ai-size-8) var(--ai-size-4);flex-direction:column;justify-content:center;align-items:center;gap:var(--ai-size-4);align-self:stretch;margin-top:auto;}.user-profile-button.svelte-14q02bh {background:none;border:none;padding:0;cursor:pointer;display:flex;justify-content:center;align-items:center;}.user-profile-button.svelte-14q02bh:focus {outline:var(--ai-size-2) solid var(--ai-color-blue-600);outline-offset:var(--ai-size-2);border-radius:var(--ai-size-4);}.user-profile-container.svelte-14q02bh {width:var(--ai-size-40);height:var(--ai-size-40);aspect-ratio:1/1;}.user-profile-icon.svelte-14q02bh {padding:0.5556rem 0.4931rem 0.5694rem 0.3819rem;}.user-initials.svelte-14q02bh {font-size:var(--ai-size-16);font-style:normal;}.menu-section.svelte-14q02bh {display:flex;padding:var(--ai-size-4);justify-content:center;align-items:center;align-self:stretch;}.menu-button.svelte-14q02bh {display:flex;padding:var(--ai-size-4);justify-content:center;align-items:center;background:none;border:none;cursor:pointer;opacity:1;transition:opacity 0.2s ease;will-change:opacity;}.menu-button.disabled.svelte-14q02bh {cursor:not-allowed;opacity:0.5;will-change:opacity;}.menu-button.svelte-14q02bh:focus,
  .nav-item.svelte-14q02bh:focus {outline:none;}.menu-button.svelte-14q02bh:focus .icon-container:where(.svelte-14q02bh),
  .nav-item.svelte-14q02bh:focus .icon-container:where(.svelte-14q02bh) {outline:var(--ai-size-2) solid var(--ai-color-blue-600);outline-offset:var(--ai-size-2);border-radius:var(--ai-size-4);}.nav-items.svelte-14q02bh {display:flex;width:var(--ai-size-72);flex-direction:column;align-items:flex-start;gap:var(--ai-size-4);}.nav-item.svelte-14q02bh {display:flex;height:4.125rem;padding:var(--ai-size-4);flex-direction:column;justify-content:center;align-items:center;gap:var(--ai-size-2);align-self:stretch;cursor:pointer;text-decoration:none;color:inherit;position:relative;flex-shrink:0;}.icon-container.svelte-14q02bh {display:flex;width:var(--ai-size-40);height:var(--ai-size-40);padding:var(--ai-size-8);justify-content:center;align-items:center;flex-shrink:0;position:relative;contain:layout style;transition:background-color 0.3s ease,
    border-radius 0.3s ease;}.icon-wrapper.svelte-14q02bh {display:flex;width:var(--ai-size-28);height:var(--ai-size-28);justify-content:center;align-items:center;flex-shrink:0;aspect-ratio:1/1;position:relative;overflow:hidden;transition:transform 0.3s ease;}.icon-wrapper.svelte-14q02bh svg {width:var(--ai-size-24);height:var(--ai-size-24);flex-shrink:0;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);transition:all 0.3s ease;}.icon-wrapper.svelte-14q02bh svg path {stroke-width:1.5;vector-effect:non-scaling-stroke;transition:stroke 0.3s ease,
    fill 0.3s ease,
    stroke-width 0.3s ease;}.nav-text.svelte-14q02bh {font-size:var(--ai-size-12);text-align:center;color:var(--ai-color-neutral-900);line-height:1.3;}.menu-button.disabled.svelte-14q02bh:hover .icon-container:where(.svelte-14q02bh),
  .menu-button.svelte-14q02bh:disabled:hover .icon-container:where(.svelte-14q02bh) {background:none !important;border-radius:var(--ai-size-4);}.nav-item.hovered.svelte-14q02bh .icon-container:where(.svelte-14q02bh),
  .nav-item.svelte-14q02bh:active .icon-container:where(.svelte-14q02bh),
  .nav-item.selected.svelte-14q02bh .icon-container:where(.svelte-14q02bh),
  .nav-item.selected.hovered.svelte-14q02bh .icon-container:where(.svelte-14q02bh),
  .menu-button.svelte-14q02bh:hover .icon-container:where(.svelte-14q02bh),
  .menu-button.svelte-14q02bh:active .icon-container:where(.svelte-14q02bh) {background-color:var(--ai-color-steel-300);border-radius:var(--ai-size-4);}.nav-item.svelte-14q02bh {flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--ai-size-4);text-decoration:none;color:var(--ai-color-neutral-600);transition:color 0.2s ease;height:100%;}.nav-item.svelte-14q02bh:hover {color:var(--ai-color-neutral-700);}`
};
function Pi(e, t) {
	B(t, !0), bt(e, Os);
	const [r, n] = Ut(),
		i = () => fe(tt, '$selectedItem', r),
		o = () => fe(me, '$isExpanded', r),
		s = () => fe(Fr, '$isMenuButtonDisabled', r),
		a = qi(),
		l = y(t, 'ssrSelectedItem', 7, void 0),
		u = y(t, 'onNavToggle', 7, void 0),
		d = y(t, 'onNavItemClick', 7, void 0),
		f = y(t, 'onProfileClick', 7, void 0),
		c = y(t, 'children', 7),
		v = y(t, 'apiUrl', 7),
		b = y(t, 'chatUrl', 7),
		g = y(t, 'consoleUrl', 7),
		_ = y(t, 'discoverUrl', 7);
	let m = !1,
		h,
		p = /* @__PURE__ */ ge(null),
		w = /* @__PURE__ */ ge(!1),
		x = /* @__PURE__ */ ge(!1);
	function M(N, K) {
		return m ? N : K;
	}
	function $(N) {
		return M(i() === N, l() === N);
	}
	function T(N) {
		return E(p) === N;
	}
	const q = /* @__PURE__ */ J(() => () => M(o(), l() ? l() !== 'discover' : !0)),
		H = /* @__PURE__ */ J(() => () => M(s(), l() ? l() === 'discover' : !1)),
		ae = /* @__PURE__ */ J(() => () => M(o(), l() ? l() !== 'discover' : !0)),
		Ve = /* @__PURE__ */ J(() => () => E(x) && E(w) && !E(H)());
	nr(() => {
		if (
			((m = !0),
			document.documentElement.classList.remove('nav-initially-expanded'),
			document.documentElement.style.removeProperty('--initial-main-top-padding'),
			h && h.classList.add('hydrated'),
			typeof window < 'u')
		) {
			const N = window.matchMedia('(hover: hover)');
			O(x, N.matches, !0),
				N.addEventListener('change', (D) => {
					O(x, D.matches, !0);
				});
			const K = (D) => {
				const Re = D,
					{ isMobile: We } = Re.detail;
				if (We) Ie.setExpanded(!1);
				else {
					const Ke = i();
					Ke && Ke !== 'discover' ? Ie.setExpanded(!0) : Ie.setExpanded(!1);
				}
			};
			return (
				window.addEventListener('layoutTransition', K),
				() => {
					window.removeEventListener('layoutTransition', K);
				}
			);
		}
	});
	function Y(N) {
		var K;
		if (
			(N !== 'discover' &&
				typeof window < 'u' &&
				sessionStorage.setItem('navigatingToMainNav', 'true'),
			Ie.setSelectedItem(N),
			N !== 'discover')
		)
			try {
				Promise.resolve()
					.then(() => Zt)
					.then(({ selectedSubNavItemTitle: D }) => {
						D.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId')),
							setTimeout(() => {
								const Re = document.getElementById('main-content');
								if (Re) {
									const We = Re.querySelector('h2[id], h3[id], h4[id], h5[id], h6[id]');
									if (We && We.textContent) {
										const Ke = We.textContent.trim();
										D.set(Ke),
											typeof window < 'u' &&
												sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(Ke));
									}
								}
							}, 200);
					})
					.catch((D) => {
						console.log('[DesktopSideNav] Error clearing header state:', D);
					});
			} catch (D) {
				console.log('[DesktopSideNav] Import failed for clearing state:', D);
			}
		else
			try {
				Promise.resolve()
					.then(() => Zt)
					.then(({ selectedSubNavItemTitle: D }) => {
						D.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId'));
					})
					.catch((D) => {
						console.log('[DesktopSideNav] Error clearing header state for Discover:', D);
					});
			} catch (D) {
				console.log('[DesktopSideNav] Import failed for clearing Discover state:', D);
			}
		(K = d()) == null || K({ item: N, expanded: o() });
	}
	function ye(N, K) {
		(N.key === 'Enter' || N.key === ' ') && (N.preventDefault(), Y(K));
	}
	var xe = As();
	let pt;
	var sr = S(xe),
		lr = S(sr),
		ur = S(lr),
		pe = S(ur);
	let Gr;
	pe.__click = [xs, s, u, o];
	var Xr = S(pe),
		Qr = S(Xr),
		Ri = S(Qr);
	const Bi = /* @__PURE__ */ J(() => E(Ve)()),
		Fi = /* @__PURE__ */ J(() => E(H)()),
		Yi = /* @__PURE__ */ J(() => E(ae)());
	Kr(Ri, {
		get isHovered() {
			return E(Bi);
		},
		get isDisabled() {
			return E(Fi);
		},
		get isOpen() {
			return E(Yi);
		}
	}),
		C(Qr),
		C(Xr),
		C(pe),
		C(ur);
	var en = W(ur, 2),
		Ee = S(en);
	let tn;
	(Ee.__click = [Es, Y]), (Ee.__keydown = [Cs, ye]);
	var rn = S(Ee),
		nn = S(rn),
		Zi = S(nn);
	const Ji = /* @__PURE__ */ J(() => $('discover'));
	Yr(Zi, {
		get isSelected() {
			return E(Ji);
		}
	}),
		C(nn),
		C(rn),
		Ne(2),
		C(Ee);
	var Ce = W(Ee, 2);
	let on;
	(Ce.__click = [Ss, Y]), (Ce.__keydown = [$s, ye]);
	var an = S(Ce),
		sn = S(an),
		Wi = S(sn);
	const Ki = /* @__PURE__ */ J(() => $('chat'));
	Zr(Wi, {
		get isSelected() {
			return E(Ki);
		}
	}),
		C(sn),
		C(an),
		Ne(2),
		C(Ce);
	var Se = W(Ce, 2);
	let ln;
	(Se.__click = [Ns, Y]), (Se.__keydown = [Is, ye]);
	var un = S(Se),
		cn = S(un),
		Gi = S(cn);
	const Xi = /* @__PURE__ */ J(() => $('console'));
	Jr(Gi, {
		get isSelected() {
			return E(Xi);
		}
	}),
		C(cn),
		C(un),
		Ne(2),
		C(Se);
	var Me = W(Se, 2);
	let dn;
	(Me.__click = [Ts, Y]), (Me.__keydown = [zs, ye]);
	var fn = S(Me),
		vn = S(fn),
		Qi = S(vn);
	const eo = /* @__PURE__ */ J(() => $('api'));
	Wr(Qi, {
		get isSelected() {
			return E(eo);
		}
	}),
		C(vn),
		C(fn),
		Ne(2),
		C(Me),
		C(en),
		C(lr);
	var hn = W(lr, 2),
		bn = S(hn);
	(bn.__click = [Ms, f]), (bn.__keydown = [Ls, f]), C(hn), C(sr);
	var cr = W(sr, 2);
	let pn;
	var to = S(cr);
	{
		var ro = (N) => {
			var K = Us(),
				D = W(S(K), 2),
				Re = S(D);
			Cr(Re, () => c() ?? ve), C(D), C(K), I(N, K);
		};
		de(to, (N) => {
			E(q)() && N(ro);
		});
	}
	C(cr),
		C(xe),
		Ni(
			xe,
			(N) => (h = N),
			() => h
		),
		ft(
			(N, K, D, Re, We, Ke, io, oo, ao, so, lo, uo, co) => {
				(pt = ue(xe, 1, 'side-nav-container svelte-14q02bh', null, pt, N)),
					(Gr = ue(pe, 1, 'menu-button svelte-14q02bh', null, Gr, K)),
					(pe.disabled = s()),
					G(pe, 'aria-label', D),
					G(pe, 'aria-expanded', Re),
					G(Ee, 'href', _() || a.discover),
					(tn = ue(Ee, 1, 'nav-item discover-item svelte-14q02bh', null, tn, We)),
					G(Ee, 'aria-current', Ke),
					G(Ce, 'href', b() || a.chat),
					(on = ue(Ce, 1, 'nav-item chat-item svelte-14q02bh', null, on, io)),
					G(Ce, 'aria-current', oo),
					G(Se, 'href', g() || a.console),
					(ln = ue(Se, 1, 'nav-item console-item svelte-14q02bh', null, ln, ao)),
					G(Se, 'aria-current', so),
					G(Me, 'href', v() || a.api),
					(dn = ue(Me, 1, 'nav-item api-item svelte-14q02bh', null, dn, lo)),
					G(Me, 'aria-current', uo),
					(pn = ue(cr, 1, 'expanded-content drawer-anim svelte-14q02bh', null, pn, co));
			},
			[
				() => ({ expanded: E(q)() }),
				() => ({ disabled: s() }),
				() => (E(q)() ? 'Close navigation menu' : 'Open navigation menu'),
				() => (E(q)() ? 'true' : 'false'),
				() => ({
					selected: $('discover'),
					hovered: T('discover')
				}),
				() => ($('discover') ? 'page' : void 0),
				() => ({
					selected: $('chat'),
					hovered: T('chat')
				}),
				() => ($('chat') ? 'page' : void 0),
				() => ({
					selected: $('console'),
					hovered: T('console')
				}),
				() => ($('console') ? 'page' : void 0),
				() => ({ selected: $('api'), hovered: T('api') }),
				() => ($('api') ? 'page' : void 0),
				() => ({ open: E(q)() })
			]
		),
		Z('mouseenter', pe, () => O(w, !0)),
		Z('mouseleave', pe, () => O(w, !1)),
		Z('focus', pe, () => O(w, !0)),
		Z('blur', pe, () => O(w, !1)),
		Z('mouseenter', Ee, () => O(p, 'discover')),
		Z('mouseleave', Ee, () => O(p, null)),
		Z('mouseenter', Ce, () => O(p, 'chat')),
		Z('mouseleave', Ce, () => O(p, null)),
		Z('mouseenter', Se, () => O(p, 'console')),
		Z('mouseleave', Se, () => O(p, null)),
		Z('mouseenter', Me, () => O(p, 'api')),
		Z('mouseleave', Me, () => O(p, null)),
		I(e, xe);
	var no = F({
		get ssrSelectedItem() {
			return l();
		},
		set ssrSelectedItem(N = void 0) {
			l(N), k();
		},
		get onNavToggle() {
			return u();
		},
		set onNavToggle(N = void 0) {
			u(N), k();
		},
		get onNavItemClick() {
			return d();
		},
		set onNavItemClick(N = void 0) {
			d(N), k();
		},
		get onProfileClick() {
			return f();
		},
		set onProfileClick(N = void 0) {
			f(N), k();
		},
		get children() {
			return c();
		},
		set children(N) {
			c(N), k();
		},
		get apiUrl() {
			return v();
		},
		set apiUrl(N) {
			v(N), k();
		},
		get chatUrl() {
			return b();
		},
		set chatUrl(N) {
			b(N), k();
		},
		get consoleUrl() {
			return g();
		},
		set consoleUrl(N) {
			g(N), k();
		},
		get discoverUrl() {
			return _();
		},
		set discoverUrl(N) {
			_(N), k();
		}
	});
	return n(), no;
}
Hr(['click', 'keydown']);
R(
	Pi,
	{
		ssrSelectedItem: {},
		onNavToggle: {},
		onNavItemClick: {},
		onProfileClick: {},
		children: {},
		apiUrl: {},
		chatUrl: {},
		consoleUrl: {},
		discoverUrl: {}
	},
	[],
	[],
	!0
);
function qs(e, t) {
	B(t, !0);
	let r = y(t, 'ssrSelectedItem', 7),
		n = y(t, 'onNavToggle', 7),
		i = y(t, 'onNavItemClick', 7),
		o = y(t, 'onProfileClick', 7),
		s = y(t, 'apiUrl', 7),
		a = y(t, 'chatUrl', 7),
		l = y(t, 'consoleUrl', 7),
		u = y(t, 'discoverUrl', 7);
	return (
		Pi(e, {
			get ssrSelectedItem() {
				return r();
			},
			get onNavToggle() {
				return n();
			},
			get onNavItemClick() {
				return i();
			},
			get onProfileClick() {
				return o();
			},
			get apiUrl() {
				return s();
			},
			get chatUrl() {
				return a();
			},
			get consoleUrl() {
				return l();
			},
			get discoverUrl() {
				return u();
			},
			children: (d, f) => {
				var c = Lt(),
					v = Ye(c);
				ir(v, t, 'default', {}), I(d, c);
			},
			$$slots: { default: !0 }
		}),
		F({
			get ssrSelectedItem() {
				return r();
			},
			set ssrSelectedItem(d) {
				r(d), k();
			},
			get onNavToggle() {
				return n();
			},
			set onNavToggle(d) {
				n(d), k();
			},
			get onNavItemClick() {
				return i();
			},
			set onNavItemClick(d) {
				i(d), k();
			},
			get onProfileClick() {
				return o();
			},
			set onProfileClick(d) {
				o(d), k();
			},
			get apiUrl() {
				return s();
			},
			set apiUrl(d) {
				s(d), k();
			},
			get chatUrl() {
				return a();
			},
			set chatUrl(d) {
				a(d), k();
			},
			get consoleUrl() {
				return l();
			},
			set consoleUrl(d) {
				l(d), k();
			},
			get discoverUrl() {
				return u();
			},
			set discoverUrl(d) {
				u(d), k();
			}
		})
	);
}
customElements.define(
	'graymatter-desktop-side-nav',
	R(
		qs,
		{
			ssrSelectedItem: {},
			onNavToggle: {},
			onNavItemClick: {},
			onProfileClick: {},
			apiUrl: {},
			chatUrl: {},
			consoleUrl: {},
			discoverUrl: {}
		},
		['default'],
		[],
		!0
	)
);
var Ps = /* @__PURE__ */ j(
	'<svg><g><path d="M57.5791 20.4177C57.5791 15.5645 53.7309 11.6187 48.9988 11.6187H40.188C38.6688 11.6187 37.4304 10.3567 37.4304 8.80197C37.4304 7.24729 38.6658 5.98522 40.188 5.98522H56.2373V0H38.8757C34.1466 0 30.2983 3.94877 30.2983 8.80197C30.2983 13.6552 34.1466 17.6039 38.8757 17.6039H47.6865C49.2057 17.6039 50.4442 18.866 50.4442 20.4207C50.4442 21.9754 49.2087 23.2374 47.6865 23.2374H31.0284V29.2197H48.9988C53.7309 29.2197 57.5791 25.2709 57.5791 20.4207V20.4177Z"></path><path d="M82.1466 24.7153L82.188 24.7685L83.6747 29.2197H90.6914L80.917 0H68.3525L58.5781 29.2167H65.5949L67.0993 24.7153H82.1466ZM69.186 18.4818L73.3653 5.98522H75.9013L75.919 6.03547L80.1161 18.5793H69.1535L69.186 18.4818Z"></path><path d="M27.198 0H20.5626V15.8158C20.5626 21.4345 16.9626 23.4355 13.5961 23.4355C9.03842 23.4355 6.62956 20.799 6.62956 15.8158V0H0V17.0069C0 20.6985 1.46305 24.0532 4.11724 26.4532C6.64729 28.7409 10.0138 30 13.599 30C20.3586 30 27.198 25.5369 27.198 17.0039V0Z"></path><path d="M101.708 9.78915H93.8662V29.2197H101.708V9.78915Z"></path><path d="M101.823 0H93.8662V5.98227H101.823V0Z"></path></g></svg>'
);
function ar(e, t) {
	const r = Yt(t, ['children', '$$slots', '$$events', '$$legacy', '$$host']),
		n = Yt(r, ['isInverted', 'width', 'height']);
	B(t, !1);
	const i = /* @__PURE__ */ zt();
	let o = y(t, 'isInverted', 12, !1),
		s = y(t, 'width', 12),
		a = y(t, 'height', 12);
	const l = 102,
		u = 30;
	s(s() || l),
		a(a() || u),
		Ko(
			() => pi(o()),
			() => {
				O(i, o() ? 'var(--ai-color-white)' : 'var(--ai-color-black)');
			}
		),
		Go();
	var d = Ps();
	return (
		Ft(d, () => ({
			width: s(),
			height: a(),
			viewBox: '0 0 102 30',
			fill: 'none',
			xmlns: 'http://www.w3.org/2000/svg',
			style: `fill: ${E(i) ?? ''};`,
			...n
		})),
		I(e, d),
		F({
			get isInverted() {
				return o();
			},
			set isInverted(f) {
				o(f), k();
			},
			get width() {
				return s();
			},
			set width(f) {
				s(f), k();
			},
			get height() {
				return a();
			},
			set height(f) {
				a(f), k();
			}
		})
	);
}
R(ar, { isInverted: {}, width: {}, height: {} }, [], [], !0);
function Ds(e, t) {
	B(t, !0);
	let r = y(t, 'isInverted', 7, !1),
		n = y(t, 'width', 7, void 0),
		i = y(t, 'height', 7, void 0),
		o = /* @__PURE__ */ Br(t, [
			'$$slots',
			'$$events',
			'$$legacy',
			'$$host',
			'isInverted',
			'width',
			'height'
		]);
	return (
		ar(
			e,
			Ti(
				{
					get isInverted() {
						return r();
					},
					get width() {
						return n();
					},
					get height() {
						return i();
					}
				},
				() => o
			)
		),
		F({
			get isInverted() {
				return r();
			},
			set isInverted(s = !1) {
				r(s), k();
			},
			get width() {
				return n();
			},
			set width(s = void 0) {
				n(s), k();
			},
			get height() {
				return i();
			},
			set height(s = void 0) {
				i(s), k();
			}
		})
	);
}
customElements.define(
	'graymatter-logo',
	R(
		Ds,
		{
			isInverted: { attribute: 'is-inverted', reflect: !0, type: 'Boolean' },
			width: { attribute: 'width', reflect: !0, type: 'Number' },
			height: { attribute: 'height', reflect: !0, type: 'Number' }
		},
		[],
		[],
		!0
	)
);
function js(e) {
	return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, 'default') ? e.default : e;
}
var Pt = { exports: {} },
	Hs = Pt.exports,
	Un;
function Vs() {
	return (
		Un ||
			((Un = 1),
			(function (e, t) {
				(function (r, n, i) {
					(e.exports = i()), (e.exports.default = i());
				})('slugify', Hs, function () {
					var r = JSON.parse(
							`{"$":"dollar","%":"percent","&":"and","<":"less",">":"greater","|":"or","¢":"cent","£":"pound","¤":"currency","¥":"yen","©":"(c)","ª":"a","®":"(r)","º":"o","À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Æ":"AE","Ç":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","Þ":"TH","ß":"ss","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","æ":"ae","ç":"c","è":"e","é":"e","ê":"e","ë":"e","ì":"i","í":"i","î":"i","ï":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","þ":"th","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Č":"C","č":"c","Ď":"D","ď":"d","Đ":"DJ","đ":"dj","Ē":"E","ē":"e","Ė":"E","ė":"e","Ę":"e","ę":"e","Ě":"E","ě":"e","Ğ":"G","ğ":"g","Ģ":"G","ģ":"g","Ĩ":"I","ĩ":"i","Ī":"i","ī":"i","Į":"I","į":"i","İ":"I","ı":"i","Ķ":"k","ķ":"k","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ł":"L","ł":"l","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","Ō":"O","ō":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","Ŕ":"R","ŕ":"r","Ř":"R","ř":"r","Ś":"S","ś":"s","Ş":"S","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","Ť":"T","ť":"t","Ũ":"U","ũ":"u","Ū":"u","ū":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","Ə":"E","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","ǈ":"LJ","ǉ":"lj","ǋ":"NJ","ǌ":"nj","Ș":"S","ș":"s","Ț":"T","ț":"t","ə":"e","˚":"o","Ά":"A","Έ":"E","Ή":"H","Ί":"I","Ό":"O","Ύ":"Y","Ώ":"W","ΐ":"i","Α":"A","Β":"B","Γ":"G","Δ":"D","Ε":"E","Ζ":"Z","Η":"H","Θ":"8","Ι":"I","Κ":"K","Λ":"L","Μ":"M","Ν":"N","Ξ":"3","Ο":"O","Π":"P","Ρ":"R","Σ":"S","Τ":"T","Υ":"Y","Φ":"F","Χ":"X","Ψ":"PS","Ω":"W","Ϊ":"I","Ϋ":"Y","ά":"a","έ":"e","ή":"h","ί":"i","ΰ":"y","α":"a","β":"b","γ":"g","δ":"d","ε":"e","ζ":"z","η":"h","θ":"8","ι":"i","κ":"k","λ":"l","μ":"m","ν":"n","ξ":"3","ο":"o","π":"p","ρ":"r","ς":"s","σ":"s","τ":"t","υ":"y","φ":"f","χ":"x","ψ":"ps","ω":"w","ϊ":"i","ϋ":"y","ό":"o","ύ":"y","ώ":"w","Ё":"Yo","Ђ":"DJ","Є":"Ye","І":"I","Ї":"Yi","Ј":"J","Љ":"LJ","Њ":"NJ","Ћ":"C","Џ":"DZ","А":"A","Б":"B","В":"V","Г":"G","Д":"D","Е":"E","Ж":"Zh","З":"Z","И":"I","Й":"J","К":"K","Л":"L","М":"M","Н":"N","О":"O","П":"P","Р":"R","С":"S","Т":"T","У":"U","Ф":"F","Х":"H","Ц":"C","Ч":"Ch","Ш":"Sh","Щ":"Sh","Ъ":"U","Ы":"Y","Ь":"","Э":"E","Ю":"Yu","Я":"Ya","а":"a","б":"b","в":"v","г":"g","д":"d","е":"e","ж":"zh","з":"z","и":"i","й":"j","к":"k","л":"l","м":"m","н":"n","о":"o","п":"p","р":"r","с":"s","т":"t","у":"u","ф":"f","х":"h","ц":"c","ч":"ch","ш":"sh","щ":"sh","ъ":"u","ы":"y","ь":"","э":"e","ю":"yu","я":"ya","ё":"yo","ђ":"dj","є":"ye","і":"i","ї":"yi","ј":"j","љ":"lj","њ":"nj","ћ":"c","ѝ":"u","џ":"dz","Ґ":"G","ґ":"g","Ғ":"GH","ғ":"gh","Қ":"KH","қ":"kh","Ң":"NG","ң":"ng","Ү":"UE","ү":"ue","Ұ":"U","ұ":"u","Һ":"H","һ":"h","Ә":"AE","ә":"ae","Ө":"OE","ө":"oe","Ա":"A","Բ":"B","Գ":"G","Դ":"D","Ե":"E","Զ":"Z","Է":"E'","Ը":"Y'","Թ":"T'","Ժ":"JH","Ի":"I","Լ":"L","Խ":"X","Ծ":"C'","Կ":"K","Հ":"H","Ձ":"D'","Ղ":"GH","Ճ":"TW","Մ":"M","Յ":"Y","Ն":"N","Շ":"SH","Չ":"CH","Պ":"P","Ջ":"J","Ռ":"R'","Ս":"S","Վ":"V","Տ":"T","Ր":"R","Ց":"C","Փ":"P'","Ք":"Q'","Օ":"O''","Ֆ":"F","և":"EV","ء":"a","آ":"aa","أ":"a","ؤ":"u","إ":"i","ئ":"e","ا":"a","ب":"b","ة":"h","ت":"t","ث":"th","ج":"j","ح":"h","خ":"kh","د":"d","ذ":"th","ر":"r","ز":"z","س":"s","ش":"sh","ص":"s","ض":"dh","ط":"t","ظ":"z","ع":"a","غ":"gh","ف":"f","ق":"q","ك":"k","ل":"l","م":"m","ن":"n","ه":"h","و":"w","ى":"a","ي":"y","ً":"an","ٌ":"on","ٍ":"en","َ":"a","ُ":"u","ِ":"e","ْ":"","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","پ":"p","چ":"ch","ژ":"zh","ک":"k","گ":"g","ی":"y","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","฿":"baht","ა":"a","ბ":"b","გ":"g","დ":"d","ე":"e","ვ":"v","ზ":"z","თ":"t","ი":"i","კ":"k","ლ":"l","მ":"m","ნ":"n","ო":"o","პ":"p","ჟ":"zh","რ":"r","ს":"s","ტ":"t","უ":"u","ფ":"f","ქ":"k","ღ":"gh","ყ":"q","შ":"sh","ჩ":"ch","ც":"ts","ძ":"dz","წ":"ts","ჭ":"ch","ხ":"kh","ჯ":"j","ჰ":"h","Ṣ":"S","ṣ":"s","Ẁ":"W","ẁ":"w","Ẃ":"W","ẃ":"w","Ẅ":"W","ẅ":"w","ẞ":"SS","Ạ":"A","ạ":"a","Ả":"A","ả":"a","Ấ":"A","ấ":"a","Ầ":"A","ầ":"a","Ẩ":"A","ẩ":"a","Ẫ":"A","ẫ":"a","Ậ":"A","ậ":"a","Ắ":"A","ắ":"a","Ằ":"A","ằ":"a","Ẳ":"A","ẳ":"a","Ẵ":"A","ẵ":"a","Ặ":"A","ặ":"a","Ẹ":"E","ẹ":"e","Ẻ":"E","ẻ":"e","Ẽ":"E","ẽ":"e","Ế":"E","ế":"e","Ề":"E","ề":"e","Ể":"E","ể":"e","Ễ":"E","ễ":"e","Ệ":"E","ệ":"e","Ỉ":"I","ỉ":"i","Ị":"I","ị":"i","Ọ":"O","ọ":"o","Ỏ":"O","ỏ":"o","Ố":"O","ố":"o","Ồ":"O","ồ":"o","Ổ":"O","ổ":"o","Ỗ":"O","ỗ":"o","Ộ":"O","ộ":"o","Ớ":"O","ớ":"o","Ờ":"O","ờ":"o","Ở":"O","ở":"o","Ỡ":"O","ỡ":"o","Ợ":"O","ợ":"o","Ụ":"U","ụ":"u","Ủ":"U","ủ":"u","Ứ":"U","ứ":"u","Ừ":"U","ừ":"u","Ử":"U","ử":"u","Ữ":"U","ữ":"u","Ự":"U","ự":"u","Ỳ":"Y","ỳ":"y","Ỵ":"Y","ỵ":"y","Ỷ":"Y","ỷ":"y","Ỹ":"Y","ỹ":"y","–":"-","‘":"'","’":"'","“":"\\"","”":"\\"","„":"\\"","†":"+","•":"*","…":"...","₠":"ecu","₢":"cruzeiro","₣":"french franc","₤":"lira","₥":"mill","₦":"naira","₧":"peseta","₨":"rupee","₩":"won","₪":"new shequel","₫":"dong","€":"euro","₭":"kip","₮":"tugrik","₯":"drachma","₰":"penny","₱":"peso","₲":"guarani","₳":"austral","₴":"hryvnia","₵":"cedi","₸":"kazakhstani tenge","₹":"indian rupee","₺":"turkish lira","₽":"russian ruble","₿":"bitcoin","℠":"sm","™":"tm","∂":"d","∆":"delta","∑":"sum","∞":"infinity","♥":"love","元":"yuan","円":"yen","﷼":"rial","ﻵ":"laa","ﻷ":"laa","ﻹ":"lai","ﻻ":"la"}`
						),
						n = JSON.parse(
							'{"bg":{"Й":"Y","Ц":"Ts","Щ":"Sht","Ъ":"A","Ь":"Y","й":"y","ц":"ts","щ":"sht","ъ":"a","ь":"y"},"de":{"Ä":"AE","ä":"ae","Ö":"OE","ö":"oe","Ü":"UE","ü":"ue","ß":"ss","%":"prozent","&":"und","|":"oder","∑":"summe","∞":"unendlich","♥":"liebe"},"es":{"%":"por ciento","&":"y","<":"menor que",">":"mayor que","|":"o","¢":"centavos","£":"libras","¤":"moneda","₣":"francos","∑":"suma","∞":"infinito","♥":"amor"},"fr":{"%":"pourcent","&":"et","<":"plus petit",">":"plus grand","|":"ou","¢":"centime","£":"livre","¤":"devise","₣":"franc","∑":"somme","∞":"infini","♥":"amour"},"pt":{"%":"porcento","&":"e","<":"menor",">":"maior","|":"ou","¢":"centavo","∑":"soma","£":"libra","∞":"infinito","♥":"amor"},"uk":{"И":"Y","и":"y","Й":"Y","й":"y","Ц":"Ts","ц":"ts","Х":"Kh","х":"kh","Щ":"Shch","щ":"shch","Г":"H","г":"h"},"vi":{"Đ":"D","đ":"d"},"da":{"Ø":"OE","ø":"oe","Å":"AA","å":"aa","%":"procent","&":"og","|":"eller","$":"dollar","<":"mindre end",">":"større end"},"nb":{"&":"og","Å":"AA","Æ":"AE","Ø":"OE","å":"aa","æ":"ae","ø":"oe"},"it":{"&":"e"},"nl":{"&":"en"},"sv":{"&":"och","Å":"AA","Ä":"AE","Ö":"OE","å":"aa","ä":"ae","ö":"oe"}}'
						);
					function i(o, s) {
						if (typeof o != 'string') throw new Error('slugify: string argument expected');
						s = typeof s == 'string' ? { replacement: s } : s || {};
						var a = n[s.locale] || {},
							l = s.replacement === void 0 ? '-' : s.replacement,
							u = s.trim === void 0 ? !0 : s.trim,
							d = o
								.normalize()
								.split('')
								.reduce(function (f, c) {
									var v = a[c];
									return (
										v === void 0 && (v = r[c]),
										v === void 0 && (v = c),
										v === l && (v = ' '),
										f + v.replace(s.remove || /[^\w\s$*_+~.()'"!\-:@]+/g, '')
									);
								}, '');
						return (
							s.strict && (d = d.replace(/[^A-Za-z0-9\s]/g, '')),
							u && (d = d.trim()),
							(d = d.replace(/\s+/g, l)),
							s.lower && (d = d.toLowerCase()),
							d
						);
					}
					return (
						(i.extend = function (o) {
							Object.assign(r, o);
						}),
						i
					);
				});
			})(Pt)),
		Pt.exports
	);
}
var Rs = Vs();
const Bs = /* @__PURE__ */ js(Rs);
var Fs = (e, t, r) => t(E(r).id),
	Ys = /* @__PURE__ */ be(
		'<a><div class="icon svelte-cugc62"><!></div> <span class="label svelte-cugc62"> </span></a>'
	),
	Zs = /* @__PURE__ */ be('<nav class="mobile-bottom-nav" part="mobile-bottom-nav"></nav>');
const Js = {
	hash: 'svelte-cugc62',
	code: '.nav-item.svelte-cugc62 {flex:1;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--ai-size-4);text-decoration:none;color:var(--ai-color-neutral-600);transition:color 0.2s ease;height:100%;}.nav-item.active.svelte-cugc62 {color:var(--ai-color-neutral-900);}.nav-item.active.svelte-cugc62 .icon:where(.svelte-cugc62) {background-color:var(--ai-color-neutral-200);border-radius:var(--ai-size-8);}.nav-item.svelte-cugc62:hover {color:var(--ai-color-neutral-700);}.icon.svelte-cugc62 {width:var(--ai-size-24);height:var(--ai-size-24);margin-bottom:var(--ai-size-2);display:flex;align-items:center;justify-content:center;padding:var(--ai-size-2);transition:background-color 0.2s ease;}.label.svelte-cugc62 {font-size:0.6875rem; /* 11px */font-weight:500;line-height:1;text-align:center;}'
};
function Di(e, t) {
	B(t, !0), bt(e, Js);
	const [r, n] = Ut(),
		i = () => fe(tt, '$selectedItem', r);
	let o = y(t, 'onNavItemClick', 7),
		s = y(t, 'apiUrl', 7),
		a = y(t, 'chatUrl', 7),
		l = y(t, 'consoleUrl', 7),
		u = y(t, 'discoverUrl', 7);
	const d = qi(),
		f = [
			{
				id: 'discover',
				label: 'Discover',
				icon: Yr,
				href: u() || d.discover
			},
			{
				id: 'chat',
				label: 'Chat',
				icon: Zr,
				href: a() || d.chat
			},
			{
				id: 'console',
				label: 'Console',
				icon: Jr,
				href: l() || d.console
			},
			{
				id: 'api',
				label: 'API',
				icon: Wr,
				href: s() || d.api
			}
		];
	function c(g) {
		var _;
		if (
			(g !== 'discover' &&
				typeof window < 'u' &&
				sessionStorage.setItem('navigatingToMainNav', 'true'),
			Ie.setSelectedItem(g),
			g !== 'discover')
		)
			try {
				Promise.resolve()
					.then(() => Zt)
					.then(({ selectedSubNavItemTitle: m }) => {
						m.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId')),
							setTimeout(() => {
								const h = document.getElementById('main-content');
								if (h) {
									const p = h.querySelector('h2[id], h3[id], h4[id], h5[id], h6[id]');
									if (p && p.textContent) {
										const w = p.textContent.trim();
										m.set(w),
											typeof window < 'u' &&
												sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(w));
									}
								}
							}, 200);
					})
					.catch((m) => {
						console.log('[MobileBottomNav] Error clearing header state:', m);
					});
			} catch (m) {
				console.log('[MobileBottomNav] Import failed for clearing state:', m);
			}
		else
			try {
				Promise.resolve()
					.then(() => Zt)
					.then(({ selectedSubNavItemTitle: m }) => {
						m.set(''),
							typeof window < 'u' &&
								(sessionStorage.removeItem('subNav-selectedTitle'),
								sessionStorage.removeItem('subNav-selectedId'));
					})
					.catch((m) => {
						console.log('[MobileBottomNav] Error clearing header state for Discover:', m);
					});
			} catch (m) {
				console.log('[MobileBottomNav] Import failed for clearing Discover state:', m);
			}
		(_ = o()) == null || _({ item: g, expanded: !1 });
	}
	var v = Zs();
	Ea(
		v,
		21,
		() => f,
		ya,
		(g, _) => {
			var m = Ys();
			let h;
			m.__click = [Fs, c, _];
			var p = S(m),
				w = S(p);
			const x = /* @__PURE__ */ J(() => i() === E(_).id);
			$a(
				w,
				() => E(_).icon,
				(T, q) => {
					q(T, {
						get isSelected() {
							return E(x);
						}
					});
				}
			),
				C(p);
			var M = W(p, 2),
				$ = S(M, !0);
			C(M),
				C(m),
				ft(
					(T, q) => {
						G(m, 'href', E(_).href),
							(h = ue(m, 1, 'nav-item svelte-cugc62', null, h, T)),
							G(m, 'part', q),
							Vr($, E(_).label);
					},
					[() => ({ active: i() === E(_).id }), () => `${Bs(E(_).id)}-item`]
				),
				I(g, m);
		}
	),
		C(v),
		I(e, v);
	var b = F({
		get onNavItemClick() {
			return o();
		},
		set onNavItemClick(g) {
			o(g), k();
		},
		get apiUrl() {
			return s();
		},
		set apiUrl(g) {
			s(g), k();
		},
		get chatUrl() {
			return a();
		},
		set chatUrl(g) {
			a(g), k();
		},
		get consoleUrl() {
			return l();
		},
		set consoleUrl(g) {
			l(g), k();
		},
		get discoverUrl() {
			return u();
		},
		set discoverUrl(g) {
			u(g), k();
		}
	});
	return n(), b;
}
Hr(['click']);
R(
	Di,
	{
		onNavItemClick: {},
		apiUrl: {},
		chatUrl: {},
		consoleUrl: {},
		discoverUrl: {}
	},
	[],
	[],
	!0
);
function Ws(e, t) {
	B(t, !0);
	let r = y(t, 'onNavItemClick', 7),
		n = y(t, 'apiUrl', 7),
		i = y(t, 'chatUrl', 7),
		o = y(t, 'consoleUrl', 7),
		s = y(t, 'discoverUrl', 7);
	return (
		Di(e, {
			get onNavItemClick() {
				return r();
			},
			get apiUrl() {
				return n();
			},
			get chatUrl() {
				return i();
			},
			get consoleUrl() {
				return o();
			},
			get discoverUrl() {
				return s();
			}
		}),
		F({
			get onNavItemClick() {
				return r();
			},
			set onNavItemClick(a) {
				r(a), k();
			},
			get apiUrl() {
				return n();
			},
			set apiUrl(a) {
				n(a), k();
			},
			get chatUrl() {
				return i();
			},
			set chatUrl(a) {
				i(a), k();
			},
			get consoleUrl() {
				return o();
			},
			set consoleUrl(a) {
				o(a), k();
			},
			get discoverUrl() {
				return s();
			},
			set discoverUrl(a) {
				s(a), k();
			}
		})
	);
}
customElements.define(
	'graymatter-mobile-bottom-nav',
	R(
		Ws,
		{
			onNavItemClick: {},
			apiUrl: {},
			chatUrl: {},
			consoleUrl: {},
			discoverUrl: {}
		},
		[],
		[],
		!0
	)
);
var Ks = /* @__PURE__ */ j(
	'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M6 18L18 6M6 6L18 18" stroke="#585C63" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>'
);
function ji(e) {
	var t = Ks();
	I(e, t);
}
R(ji, {}, [], [], !0);
var Gs = /* @__PURE__ */ be(
		'<div class="mobile-overlay" role="button" tabindex="0" aria-label="Close navigation menu (Escape key supported)" part="mobile-overlay"></div>'
	),
	Xs = /* @__PURE__ */ be(
		'<div class="subnav-section svelte-1xj1twt" part="subnav-section"><!></div>'
	),
	Qs = /* @__PURE__ */ be(
		'<div class="no-subnav-message svelte-1xj1twt" part="no-subnav-message"><p class="svelte-1xj1twt">Select a section from the bottom navigation</p></div>'
	),
	el = /* @__PURE__ */ be(
		'<!> <div part="mobile-side-menu"><div class="menu-header svelte-1xj1twt" part="menu-header"><div class="logo-container svelte-1xj1twt" part="logo-container"><!></div> <button class="close-button svelte-1xj1twt" aria-label="Close navigation menu" part="close-button"><!></button></div> <div class="menu-content svelte-1xj1twt" part="menu-content"><!></div></div>',
		1
	);
const tl = {
	hash: 'svelte-1xj1twt',
	code: '.no-subnav-message.svelte-1xj1twt {padding:var(--ai-size-16);text-align:center;color:var(--ai-color-steel-600);font-size:var(--ai-size-14);}.no-subnav-message.svelte-1xj1twt p:where(.svelte-1xj1twt) {margin:0;}.mobile-side-menu.svelte-1xj1twt {position:fixed;top:0;left:0;bottom:0;width:15rem;max-width:85vw;background:var(--ai-color-white);transform:translateX(-100%);transition:transform 0.3s ease;z-index:var(--ai-layer-50);flex-direction:column;box-shadow:var(--ai-size-2) 0 var(--ai-size-10) #0000001a;}.mobile-side-menu.open.svelte-1xj1twt {transform:translateX(0);}.menu-header.svelte-1xj1twt {display:flex;align-items:center;justify-content:space-between;padding:var(--ai-size-16) var(--ai-size-20);border-bottom:0.0625rem solid var(--ai-color-neutral-200);}.mobile-side-menu.svelte-1xj1twt .logo-container:where(.svelte-1xj1twt) {display:flex;}.close-button.svelte-1xj1twt {display:flex;align-items:center;justify-content:center;width:var(--ai-size-32);height:var(--ai-size-32);border:none;background:none;color:var(--ai-color-neutral-700);cursor:pointer;border-radius:var(--ai-size-6);transition:background-color 0.2s ease;}.close-button.svelte-1xj1twt:hover {background-color:var(--ai-color-neutral-100);}.menu-content.svelte-1xj1twt {flex:1;overflow-y:auto;padding:0;}.subnav-section.svelte-1xj1twt {padding:var(--ai-size-16) 0;overflow-y:auto;max-height:calc(100vh - 4rem); /* leave space for header/close */}'
};
function Hi(e, t) {
	B(t, !1), bt(e, tl);
	const [r, n] = Ut(),
		i = () => fe(me, '$isExpanded', r),
		o = () => fe(tt, '$selectedItem', r);
	let s = /* @__PURE__ */ zt(),
		a = null;
	function l(T) {
		if (((a = T.detail.hash), u(), a)) {
			const H = document.getElementById(a);
			if (H) {
				const ae = document.getElementById('main-content-mobile'),
					Ve = ae ? ae.querySelector(`[id="${a}"]`) : null;
				if (Ve && ae) {
					const Y = Ve.getBoundingClientRect(),
						ye = ae.getBoundingClientRect(),
						xe = ae.scrollTop + Y.top - ye.top;
					ae.scrollTo({ top: xe, behavior: 'auto' }),
						setTimeout(() => {
							var pt;
							window.dispatchEvent(
								new CustomEvent('sectionVisible', {
									detail: { title: (pt = Ve.textContent) == null ? void 0 : pt.trim(), id: Ve.id }
								})
							);
						}, 100);
				} else {
					let Y = H.parentElement;
					for (; Y && Y !== document.body; ) {
						const ye = window.getComputedStyle(Y);
						if (ye.overflowY === 'auto' || ye.overflowY === 'scroll') break;
						Y = Y.parentElement;
					}
					Y && Y !== document.body
						? document.getElementById('main-content')
							? (H.scrollIntoView({ behavior: 'auto', block: 'start' }),
								setTimeout(() => {
									var xe;
									window.dispatchEvent(
										new CustomEvent('sectionVisible', {
											detail: { title: (xe = H.textContent) == null ? void 0 : xe.trim(), id: H.id }
										})
									);
								}, 100))
							: H.scrollIntoView({ behavior: 'auto', block: 'start' })
						: H.scrollIntoView({ behavior: 'auto', block: 'start' });
				}
			}
			a = null;
		}
	}
	nr(() => {
		window.addEventListener('mobileAnchorNavigate', l);
		const T = () => {
			u();
		};
		window.addEventListener('closeMobileMenu', T);
		const q = (ae) => {
			const Ve = ae,
				{ isMobile: Y } = Ve.detail;
			Y && Ie.setExpanded(!1);
		};
		window.addEventListener('layoutTransition', q);
		const H = (ae) => {
			ae.key === 'Escape' && i() && u();
		};
		return (
			window.addEventListener('keydown', H),
			() => {
				window.removeEventListener('mobileAnchorNavigate', l),
					window.removeEventListener('closeMobileMenu', T),
					window.removeEventListener('layoutTransition', q),
					window.removeEventListener('keydown', H);
			}
		);
	});
	function u() {
		Ie.setExpanded(!1);
	}
	Ii();
	var d = el(),
		f = Ye(d);
	{
		var c = (T) => {
			var q = Gs();
			Z('click', q, u),
				Z('keydown', q, (H) => {
					(H.key === 'Enter' || H.key === ' ') && u();
				}),
				I(T, q);
		};
		de(f, (T) => {
			i() && T(c);
		});
	}
	var v = W(f, 2);
	let b;
	var g = S(v),
		_ = S(g),
		m = S(_);
	ar(m, { width: 75, height: 22 }), C(_);
	var h = W(_, 2),
		p = S(h);
	ji(p), C(h), C(g);
	var w = W(g, 2),
		x = S(w);
	{
		var M = (T) => {
				var q = Xs(),
					H = S(q);
				ir(H, t, 'default', {}), C(q), I(T, q);
			},
			$ = (T) => {
				var q = Qs();
				I(T, q);
			};
		de(x, (T) => {
			o() && o() !== 'discover' ? T(M) : T($, !1);
		});
	}
	C(w),
		C(v),
		Ni(
			v,
			(T) => O(s, T),
			() => E(s)
		),
		ft(
			(T) => (b = ue(v, 1, 'mobile-side-menu svelte-1xj1twt', null, b, T)),
			[() => ({ open: i() })],
			Kt
		),
		Z('click', h, u),
		I(e, d),
		F(),
		n();
}
R(Hi, {}, ['default'], [], !0);
function rl(e, t) {
	Hi(e, {
		children: (r, n) => {
			var i = Lt(),
				o = Ye(i);
			ir(o, t, 'default', {}), I(r, i);
		},
		$$slots: { default: !0 }
	});
}
customElements.define('graymatter-mobile-side-menu', R(rl, {}, ['default'], [], !0));
var nl = /* @__PURE__ */ be(
	'<nav class="mobile-top-nav svelte-1u5nbcr" part="mobile-top-nav"><button aria-label="Open navigation menu" part="menu-button"><!></button> <div class="brand svelte-1u5nbcr"><!> <span class="brand-text svelte-1u5nbcr"> </span></div> <button class="profile-button svelte-1u5nbcr" aria-label="Open profile menu" part="profile-button"><div class="user-profile-container svelte-1u5nbcr"><div class="user-profile-icon svelte-1u5nbcr" part="user-profile-icon"><span class="user-initials svelte-1u5nbcr">ZW</span></div></div></button></nav>'
);
const il = {
	hash: 'svelte-1u5nbcr',
	code: `.user-profile-container.svelte-1u5nbcr,
  .user-profile-icon.svelte-1u5nbcr {display:flex;align-items:center;justify-content:center;}.menu-button.svelte-1u5nbcr,
  .profile-button.svelte-1u5nbcr {display:flex;align-items:center;justify-content:center;border:none;background:none;cursor:pointer;border-radius:var(--ai-size-8);transition:background-color 0.2s ease;}.menu-button.svelte-1u5nbcr:disabled,
  .menu-button.disabled.svelte-1u5nbcr {cursor:not-allowed;opacity:0.5;background-color:transparent;}.user-profile-icon.svelte-1u5nbcr {border-radius:var(--ai-size-6);background:var(--ai-color-steel-700);}.user-initials.svelte-1u5nbcr {color:var(--ai-color-white);text-align:center;font-weight:400;line-height:1.3;}.mobile-top-nav.svelte-1u5nbcr .menu-button:where(.svelte-1u5nbcr) {width:var(--ai-size-40);height:var(--ai-size-40);color:var(--ai-color-neutral-700);}.menu-button.svelte-1u5nbcr:hover {background-color:var(--ai-color-neutral-100);}

  /* MobileTopNav specific disabled state */.mobile-top-nav.svelte-1u5nbcr .menu-button.disabled:where(.svelte-1u5nbcr),
  .mobile-top-nav.svelte-1u5nbcr .menu-button:where(.svelte-1u5nbcr):disabled {cursor:not-allowed;opacity:0.5;background-color:transparent;}.brand.svelte-1u5nbcr {display:flex;align-items:center;gap:var(--ai-size-12);flex:1;margin-left:var(--ai-size-8);}.brand-text.svelte-1u5nbcr {color:var(--ai-color-neutral-900);font-size:var(--ai-size-16);font-weight:600;line-height:1;}.profile-button.svelte-1u5nbcr {width:var(--ai-size-40);height:var(--ai-size-40);}.profile-button.svelte-1u5nbcr:hover {background-color:var(--ai-color-neutral-100);}.mobile-top-nav.svelte-1u5nbcr .user-profile-icon:where(.svelte-1u5nbcr) {width:var(--ai-size-32);height:var(--ai-size-32);border-radius:0.3125rem;}.mobile-top-nav.svelte-1u5nbcr .user-initials:where(.svelte-1u5nbcr) {font-size:var(--ai-size-14);line-height:1;}.user-profile-container.svelte-1u5nbcr {width:var(--ai-size-40);height:var(--ai-size-40);aspect-ratio:1/1;}.user-profile-icon.svelte-1u5nbcr {padding:0.5556rem 0.4931rem 0.5694rem 0.3819rem;}.user-initials.svelte-1u5nbcr {font-size:var(--ai-size-16);font-style:normal;}.menu-button.svelte-1u5nbcr {display:flex;padding:var(--ai-size-4);justify-content:center;align-items:center;background:none;border:none;cursor:pointer;opacity:1;transition:opacity 0.2s ease;will-change:opacity;}.menu-button.disabled.svelte-1u5nbcr {cursor:not-allowed;opacity:0.5;will-change:opacity;}.menu-button.svelte-1u5nbcr:focus {outline:none;}`
};
function Vi(e, t) {
	B(t, !1), bt(e, il);
	const [r, n] = Ut(),
		i = () => fe(Xe, '$selectedSubNavItemTitle', r),
		o = () => fe(Fr, '$isMenuButtonDisabled', r);
	let s = y(t, 'onProfileClick', 12, void 0);
	nr(() => {
		const p = (M) => {
				if (window.innerWidth <= 640) return;
				const $ = M;
				$.detail &&
					$.detail.title &&
					(Xe.set($.detail.title),
					typeof window < 'u' &&
						sessionStorage.setItem('subNav-selectedTitle', JSON.stringify($.detail.title)));
			},
			w = () => {
				if (typeof window > 'u') return;
				const M = document.getElementById('main-content');
				if (!M) return;
				const $ = M.querySelector('h2[id], h3[id], h4[id], h5[id], h6[id]');
				if ($ && $.textContent) {
					const T = $.textContent.trim();
					i() ||
						(Xe.set(T),
						typeof window < 'u' &&
							sessionStorage.setItem('subNav-selectedTitle', JSON.stringify(T)));
				}
			};
		window.addEventListener('sectionVisible', p);
		const x = sessionStorage.getItem('subNav-selectedTitle');
		return (
			(!x || x === '""' || x === 'null') &&
				setTimeout(() => {
					w();
				}, 100),
			() => {
				window.removeEventListener('sectionVisible', p);
			}
		);
	});
	function a() {
		Ie.toggleExpanded();
	}
	function l() {
		var p;
		(p = s()) == null || p();
	}
	Ii();
	var u = nl(),
		d = S(u);
	let f;
	var c = S(d);
	Kr(c, {}), C(d);
	var v = W(d, 2),
		b = S(v);
	ar(b, { width: 75, height: 22 });
	var g = W(b, 2),
		_ = S(g, !0);
	C(g), C(v);
	var m = W(v, 2);
	C(u),
		ft(
			(p) => {
				(f = ue(d, 1, 'menu-button svelte-1u5nbcr', null, f, p)),
					(d.disabled = o()),
					Vr(_, i() || '');
			},
			[() => ({ disabled: o() })],
			Kt
		),
		Z('click', d, a),
		Z('click', m, l),
		I(e, u);
	var h = F({
		get onProfileClick() {
			return s();
		},
		set onProfileClick(p) {
			s(p), k();
		}
	});
	return n(), h;
}
R(Vi, { onProfileClick: {} }, [], [], !0);
function ol(e, t) {
	B(t, !0);
	let r = y(t, 'onMenuToggle', 7);
	return (
		Vi(e, {
			get onMenuToggle() {
				return r();
			}
		}),
		F({
			get onMenuToggle() {
				return r();
			},
			set onMenuToggle(n) {
				r(n), k();
			}
		})
	);
}
customElements.define('graymatter-mobile-top-nav', R(ol, { onMenuToggle: {} }, [], [], !0));
export {
	Ga as Button,
	rs as DesktopHeader,
	qs as DesktopSideNav,
	Ds as Logo,
	Ws as MobileBottomNav,
	rl as MobileSideMenu,
	ol as MobileTopNav
};
