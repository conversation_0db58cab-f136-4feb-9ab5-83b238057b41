<script lang="ts">
	import { blur } from 'svelte/transition';
	export let className = 'w-4 h-4';
	export let strokeWidth = '1.5';
</script>

<svg
	xmlns="http://www.w3.org/2000/svg"
	fill="none"
	viewBox="0 0 24 24"
	stroke-width={strokeWidth}
	stroke="currentColor"
	class={className + ($$restProps.class ? ' ' + $$restProps.class : '')}
	aria-label="checked"
	role="img"
	transition:blur={$$restProps.transition === 'blur' ? { duration: 300 } : undefined}
>
	<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
</svg>
